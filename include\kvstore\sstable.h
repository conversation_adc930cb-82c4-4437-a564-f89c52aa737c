#ifndef KVSTORE_SSTABLE_H_
#define KVSTORE_SSTABLE_H_

#include <string>
#include <memory>
#include "kvstore/slice.h"
#include "kvstore/status.h"

namespace kvstore {

class Comparator;

// Iterator接口的前向声明
class Iterator {
 public:
  virtual ~Iterator() = default;
  
  // 当前位置是否有效
  virtual bool Valid() const = 0;
  
  // 定位到第一个条目
  virtual void SeekToFirst() = 0;
  
  // 定位到最后一个条目
  virtual void SeekToLast() = 0;
  
  // 定位到第一个>=target的条目
  virtual void Seek(const Slice& target) = 0;
  
  // 移动到下一个条目
  virtual void Next() = 0;
  
  // 移动到上一个条目
  virtual void Prev() = 0;
  
  // 获取当前位置的key
  virtual Slice key() const = 0;
  
  // 获取当前位置的value
  virtual Slice value() const = 0;
};

// SSTable类负责读取SSTable文件
// 提供随机访问和顺序遍历功能
class SSTable {
 public:
  // 打开现有的SSTable文件
  // filename: SSTable文件路径
  // comparator: 用于key比较的比较器
  // sstable: 输出参数，成功时包含创建的SSTable实例
  static Status Open(const std::string& filename,
                     const Comparator* comparator,
                     std::unique_ptr<SSTable>* sstable);

  // 析构函数
  ~SSTable();

  // 禁止拷贝和赋值
  SSTable(const SSTable&) = delete;
  SSTable& operator=(const SSTable&) = delete;

  // 查找指定key的value
  // key: 要查找的键
  // value: 输出参数，找到时存储对应的值
  // 返回：成功找到返回OK，未找到返回NotFound，其他错误返回相应状态
  Status Get(const Slice& key, std::string* value);

  // 创建Iterator用于顺序遍历SSTable
  // 返回的Iterator需要调用者负责释放
  std::unique_ptr<Iterator> NewIterator();

  // 获取文件统计信息
  uint64_t NumEntries() const;      // 条目数量
  uint64_t FileSize() const;        // 文件大小（字节）
  Slice FirstKey() const;           // 第一个key
  Slice LastKey() const;            // 最后一个key

 private:
  // 私有构造函数，只能通过Open()创建
  explicit SSTable(const std::string& filename, const Comparator* comparator);
  
  // 加载文件元数据
  Status LoadMetadata();

  // 内部实现结构体，隐藏实现细节
  struct Rep;
  Rep* rep_;
  
  // 友元声明，允许SSTableIterator访问私有成员
  friend class SSTableIterator;
};

// SSTableIterator实现Iterator接口，用于遍历SSTable
class SSTableIterator : public Iterator {
 public:
  explicit SSTableIterator(SSTable* sstable);
  ~SSTableIterator() override;

  // 禁止拷贝和赋值
  SSTableIterator(const SSTableIterator&) = delete;
  SSTableIterator& operator=(const SSTableIterator&) = delete;

  // 实现Iterator接口
  bool Valid() const override;
  void SeekToFirst() override;
  void SeekToLast() override;
  void Seek(const Slice& target) override;
  void Next() override;
  void Prev() override;
  Slice key() const override;
  Slice value() const override;

 private:
  // 内部实现结构体
  struct Rep;
  Rep* rep_;
};

}  // namespace kvstore

#endif  // KVSTORE_SSTABLE_H_
