#include "kvstore/db.h"
#include "kvstore/memtable.h"
#include "kvstore/sstable.h"
#include "kvstore/sstable_builder.h"
#include "kvstore/comparator.h"
#include <filesystem>
#include <algorithm>
#include <mutex>
#include <cassert>

namespace kvstore {

// 默认配置
Options::Options() 
    : comparator(BytewiseComparator()),
      memtable_flush_threshold(4 * 1024 * 1024),  // 4MB
      db_path("./kvstore_data") {
}

// DB内部实现结构
struct DB::Rep {
  Options options;                              // 数据库配置
  MemTable* mem_;                              // 当前活跃的MemTable
  std::vector<std::unique_ptr<SSTable>> sstables_;  // 所有SSTable文件
  uint64_t next_file_number_;                  // 下一个文件序列号
  std::mutex mutex_;                           // 保护并发访问的互斥锁
  bool closed_;                                // 数据库是否已关闭
  
  Rep(const Options& opts) 
      : options(opts),
        mem_(nullptr),
        next_file_number_(1),
        closed_(false) {
    // 创建初始MemTable
    mem_ = new MemTable(options.comparator);
    mem_->Ref();
  }
  
  ~Rep() {
    if (mem_ != nullptr) {
      mem_->Unref();
    }
  }
};

DB::DB(const Options& options) : rep_(new Rep(options)) {
}

DB::~DB() {
  if (!rep_->closed_) {
    Close();
  }
  delete rep_;
}

Status DB::Open(const Options& options, std::unique_ptr<DB>* db) {
  auto database = std::unique_ptr<DB>(new DB(options));
  
  Status s = database->Initialize();
  if (!s.ok()) {
    return s;
  }
  
  *db = std::move(database);
  return Status::OK();
}

Status DB::Initialize() {
  // 创建数据库目录
  std::error_code ec;
  std::filesystem::create_directories(rep_->options.db_path, ec);
  if (ec) {
    return Status::IOError("无法创建数据库目录: " + ec.message());
  }
  
  // 创建sstables子目录
  std::string sstable_dir = rep_->options.db_path + "/sstables";
  std::filesystem::create_directories(sstable_dir, ec);
  if (ec) {
    return Status::IOError("无法创建SSTable目录: " + ec.message());
  }
  
  // 加载现有的SSTable文件
  Status s = LoadSSTables();
  if (!s.ok()) {
    return s;
  }
  
  return Status::OK();
}

Status DB::LoadSSTables() {
  std::string sstable_dir = rep_->options.db_path + "/sstables";
  
  // 扫描sstables目录中的所有.sst文件
  std::vector<std::pair<uint64_t, std::string>> files;
  
  std::error_code ec;
  for (const auto& entry : std::filesystem::directory_iterator(sstable_dir, ec)) {
    if (ec) {
      continue;
    }
    
    if (entry.is_regular_file() && entry.path().extension() == ".sst") {
      std::string filename = entry.path().filename().string();
      uint64_t seq_number;
      
      if (ParseSSTableFileName(filename, &seq_number)) {
        files.emplace_back(seq_number, entry.path().string());
        
        // 更新下一个文件序列号
        if (seq_number >= rep_->next_file_number_) {
          rep_->next_file_number_ = seq_number + 1;
        }
      }
    }
  }
  
  // 按序列号排序（旧文件在前）
  std::sort(files.begin(), files.end());
  
  // 加载所有SSTable文件
  for (const auto& file_info : files) {
    std::unique_ptr<SSTable> sstable;
    Status s = SSTable::Open(file_info.second, rep_->options.comparator, &sstable);
    if (s.ok()) {
      rep_->sstables_.push_back(std::move(sstable));
    } else {
      // 记录错误但继续加载其他文件
      // 在实际项目中应该有更好的错误处理
    }
  }
  
  return Status::OK();
}

uint64_t DB::GetNextFileNumber() {
  return rep_->next_file_number_++;
}

Status DB::Put(const Slice& key, const Slice& value) {
  std::lock_guard<std::mutex> lock(rep_->mutex_);
  
  if (rep_->closed_) {
    return Status::InvalidArgument("数据库已关闭");
  }
  
  // 检查是否需要刷盘
  Status s = MaybeScheduleFlush();
  if (!s.ok()) {
    return s;
  }
  
  // 将数据添加到MemTable
  rep_->mem_->Add(key, value);
  
  return Status::OK();
}

Status DB::Get(const Slice& key, std::string* value) {
  std::lock_guard<std::mutex> lock(rep_->mutex_);
  
  if (rep_->closed_) {
    return Status::InvalidArgument("数据库已关闭");
  }
  
  // 1. 先查MemTable
  Status s;
  if (rep_->mem_->Get(key, value, &s)) {
    return s.ok() ? Status::OK() : s;
  }
  
  // 2. 依次查找所有SSTable（按时间倒序，新的先查）
  for (auto rit = rep_->sstables_.rbegin(); rit != rep_->sstables_.rend(); ++rit) {
    s = (*rit)->Get(key, value);
    if (s.ok() || !s.IsNotFound()) {
      return s;
    }
  }
  
  return Status::NotFound("键未找到");
}

Status DB::Delete(const Slice& key) {
  // 简化实现：在LSM-Tree中，删除通常是写入一个特殊的墓碑标记
  // 这里我们先实现一个简单版本，直接忽略删除操作
  // 在完整实现中，应该写入删除标记到MemTable
  
  // TODO: 实现真正的删除逻辑（写入墓碑标记）
  (void)key;  // 避免未使用参数警告
  return Status::OK();
}

Status DB::MaybeScheduleFlush() {
  // 检查MemTable是否超过阈值
  if (rep_->mem_->ApproximateMemoryUsage() >= rep_->options.memtable_flush_threshold) {
    return FlushMemTable();
  }
  
  return Status::OK();
}

Status DB::FlushMemTable() {
  if (rep_->closed_) {
    return Status::InvalidArgument("数据库已关闭");
  }
  
  // 生成新的SSTable文件名
  uint64_t seq = GetNextFileNumber();
  std::string sstable_path = rep_->options.db_path + "/sstables/" + SSTableFileName(seq);
  
  // 创建SSTableBuilder
  SSTableBuilder builder(sstable_path);
  
  // 遍历MemTable，有序写入SSTable
  auto iter = rep_->mem_->NewIterator();
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    Status s = builder.Add(iter->key(), iter->value());
    if (!s.ok()) {
      builder.Abandon();
      delete iter;
      return s;
    }
  }
  delete iter;
  
  // 完成文件写入
  uint64_t file_size;
  Status s = builder.Finish(&file_size);
  if (!s.ok()) {
    return s;
  }
  
  // 加载新创建的SSTable
  std::unique_ptr<SSTable> sstable;
  s = SSTable::Open(sstable_path, rep_->options.comparator, &sstable);
  if (!s.ok()) {
    return s;
  }
  
  // 注册新SSTable到文件列表
  rep_->sstables_.push_back(std::move(sstable));
  
  // 创建新的MemTable
  rep_->mem_->Unref();
  rep_->mem_ = new MemTable(rep_->options.comparator);
  rep_->mem_->Ref();
  
  return Status::OK();
}

void DB::GetStats(Stats* stats) {
  std::lock_guard<std::mutex> lock(rep_->mutex_);
  
  stats->memtable_size = rep_->mem_->ApproximateMemoryUsage();
  stats->num_sstables = rep_->sstables_.size();
  stats->total_sstable_size = 0;
  stats->num_entries = 0;
  
  for (const auto& sstable : rep_->sstables_) {
    stats->total_sstable_size += sstable->FileSize();
    stats->num_entries += sstable->NumEntries();
  }
  
  // 估算MemTable中的条目数（简化实现）
  auto iter = rep_->mem_->NewIterator();
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    stats->num_entries++;
  }
  delete iter;
}

Status DB::Close() {
  std::lock_guard<std::mutex> lock(rep_->mutex_);
  
  if (rep_->closed_) {
    return Status::OK();
  }
  
  // 刷新当前MemTable到磁盘
  Status s = FlushMemTable();
  if (!s.ok()) {
    return s;
  }
  
  rep_->closed_ = true;
  return Status::OK();
}

}  // namespace kvstore
