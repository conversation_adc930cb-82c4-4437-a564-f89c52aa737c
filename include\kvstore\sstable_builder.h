#ifndef KVSTORE_SSTABLE_BUILDER_H_
#define KVSTORE_SSTABLE_BUILDER_H_

#include <string>
#include <memory>
#include "kvstore/slice.h"
#include "kvstore/status.h"

namespace kvstore {

class Comparator;

// SSTableBuilder负责构建SSTable文件
// 使用方法：
// 1. 创建Builder实例，指定文件名
// 2. 按顺序调用Add()添加有序的key-value对
// 3. 调用Finish()完成文件写入
// 4. 或调用Abandon()放弃构建
class SSTableBuilder {
 public:
  // 构造函数，指定输出文件名
  explicit SSTableBuilder(const std::string& filename);
  
  // 析构函数，如果没有调用Finish()会自动调用Abandon()
  ~SSTableBuilder();

  // 禁止拷贝和赋值
  SSTableBuilder(const SSTableBuilder&) = delete;
  SSTableBuilder& operator=(const SSTableBuilder&) = delete;

  // 添加一个key-value对到SSTable
  // 要求：key必须严格按字典序递增
  // 如果key顺序错误，返回InvalidArgument错误
  Status Add(const Slice& key, const Slice& value);

  // 完成SSTable文件构建
  // 写入元数据，关闭文件，执行原子重命名
  // file_size: 可选参数，返回最终文件大小
  Status Finish(uint64_t* file_size = nullptr);

  // 放弃当前构建，删除临时文件
  void Abandon();

  // 获取当前估计的文件大小（字节数）
  uint64_t FileSize() const;

  // 获取已添加的键值对数量
  uint64_t NumEntries() const;

 private:
  // 内部实现结构体，隐藏实现细节
  struct Rep;
  Rep* rep_;
};

// 辅助函数：生成SSTable文件名
// seq_number: 序列号
// 返回格式：{seq_number:06d}.sst
std::string SSTableFileName(uint64_t seq_number);

// 辅助函数：从文件名解析序列号
// 如果文件名格式不正确，返回false
bool ParseSSTableFileName(const std::string& filename, uint64_t* seq_number);

}  // namespace kvstore

#endif  // KVSTORE_SSTABLE_BUILDER_H_
