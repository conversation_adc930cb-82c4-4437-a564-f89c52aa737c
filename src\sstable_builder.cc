#include "kvstore/sstable_builder.h"
#include "kvstore/comparator.h"
#include <fstream>
#include <cassert>
#include <cstdint>
#include <sstream>
#include <iomanip>

namespace kvstore {

// 魔数标识SSTable文件格式
static const uint32_t kSSTableMagicNumber = 0x12345678;
static const uint32_t kSSTableVersion = 1;

// Varint编码辅助函数 - 复用MemTable的实现
static void PutVarint32(std::string* dst, uint32_t v) {
  char buf[5];
  char* ptr = buf;
  static const int B = 128;
  if (v < (1 << 7)) {
    *(ptr++) = v;
  } else if (v < (1 << 14)) {
    *(ptr++) = v | B;
    *(ptr++) = v >> 7;
  } else if (v < (1 << 21)) {
    *(ptr++) = v | B;
    *(ptr++) = (v >> 7) | B;
    *(ptr++) = v >> 14;
  } else if (v < (1 << 28)) {
    *(ptr++) = v | B;
    *(ptr++) = (v >> 7) | B;
    *(ptr++) = (v >> 14) | B;
    *(ptr++) = v >> 21;
  } else {
    *(ptr++) = v | B;
    *(ptr++) = (v >> 7) | B;
    *(ptr++) = (v >> 14) | B;
    *(ptr++) = (v >> 21) | B;
    *(ptr++) = v >> 28;
  }
  dst->append(buf, ptr - buf);
}

static void PutVarint64(std::string* dst, uint64_t v) {
  char buf[10];
  char* ptr = buf;
  static const int B = 128;
  while (v >= B) {
    *(ptr++) = (v & (B - 1)) | B;
    v >>= 7;
  }
  *(ptr++) = static_cast<char>(v);
  dst->append(buf, ptr - buf);
}

static void PutFixed32(std::string* dst, uint32_t value) {
  char buf[4];
  buf[0] = static_cast<char>(value & 0xff);
  buf[1] = static_cast<char>((value >> 8) & 0xff);
  buf[2] = static_cast<char>((value >> 16) & 0xff);
  buf[3] = static_cast<char>((value >> 24) & 0xff);
  dst->append(buf, 4);
}

// 简单的CRC32计算（用于校验和）
static uint32_t CalculateCRC32(const char* data, size_t length) {
  // 简化版CRC32，实际项目中应使用标准库实现
  uint32_t crc = 0xffffffff;
  for (size_t i = 0; i < length; ++i) {
    crc ^= static_cast<uint8_t>(data[i]);
    for (int j = 0; j < 8; ++j) {
      if (crc & 1) {
        crc = (crc >> 1) ^ 0xedb88320;
      } else {
        crc >>= 1;
      }
    }
  }
  return ~crc;
}

// SSTableBuilder内部实现结构
struct SSTableBuilder::Rep {
  std::string filename;       // 最终文件名
  std::string temp_filename;  // 临时文件名
  std::ofstream file;         // 文件输出流
  uint64_t offset;           // 当前文件偏移量
  uint64_t num_entries;      // 已添加的条目数
  std::string last_key;      // 上一个添加的key，用于顺序检查
  bool finished;             // 是否已完成构建
  std::string file_content;  // 文件内容缓存，用于计算校验和
  
  Rep(const std::string& fname) 
      : filename(fname),
        temp_filename(fname + ".tmp"),
        offset(0),
        num_entries(0),
        finished(false) {}
};

SSTableBuilder::SSTableBuilder(const std::string& filename) 
    : rep_(new Rep(filename)) {
  
  // 打开临时文件进行写入
  rep_->file.open(rep_->temp_filename, std::ios::binary);
  if (!rep_->file.is_open()) {
    delete rep_;
    rep_ = nullptr;
    return;
  }
  
  // 写入文件头：魔数 + 版本号
  std::string header;
  PutFixed32(&header, kSSTableMagicNumber);
  PutFixed32(&header, kSSTableVersion);
  
  rep_->file.write(header.data(), header.size());
  rep_->file_content.append(header);
  rep_->offset += header.size();
}

SSTableBuilder::~SSTableBuilder() {
  if (rep_ != nullptr) {
    if (!rep_->finished) {
      Abandon();
    }
    delete rep_;
  }
}

Status SSTableBuilder::Add(const Slice& key, const Slice& value) {
  if (rep_ == nullptr || !rep_->file.is_open()) {
    return Status::IOError("文件未正确打开");
  }
  
  if (rep_->finished) {
    return Status::InvalidArgument("构建器已完成，无法添加更多数据");
  }
  
  // 检查key的顺序性
  if (!rep_->last_key.empty()) {
    if (key.compare(Slice(rep_->last_key)) <= 0) {
      return Status::InvalidArgument("键必须按字典序严格递增");
    }
  }
  
  // 编码key-value对：[varint32:key_len][key_data][varint32:value_len][value_data]
  std::string encoded;
  PutVarint32(&encoded, static_cast<uint32_t>(key.size()));
  encoded.append(key.data(), key.size());
  PutVarint32(&encoded, static_cast<uint32_t>(value.size()));
  encoded.append(value.data(), value.size());
  
  // 写入文件
  rep_->file.write(encoded.data(), encoded.size());
  if (rep_->file.fail()) {
    return Status::IOError("写入文件失败");
  }
  
  // 更新状态
  rep_->file_content.append(encoded);
  rep_->offset += encoded.size();
  rep_->num_entries++;
  rep_->last_key.assign(key.data(), key.size());
  
  return Status::OK();
}

Status SSTableBuilder::Finish(uint64_t* file_size) {
  if (rep_ == nullptr || !rep_->file.is_open()) {
    return Status::IOError("文件未正确打开");
  }
  
  if (rep_->finished) {
    return Status::InvalidArgument("构建器已完成");
  }
  
  // 写入文件尾：条目数量 + 校验和
  std::string footer;
  PutVarint64(&footer, rep_->num_entries);
  
  // 计算整个文件内容的校验和
  uint32_t checksum = CalculateCRC32(rep_->file_content.data(), rep_->file_content.size());
  PutFixed32(&footer, checksum);
  
  // 写入footer
  rep_->file.write(footer.data(), footer.size());
  if (rep_->file.fail()) {
    return Status::IOError("写入文件尾失败");
  }
  
  rep_->offset += footer.size();
  
  // 强制刷盘并关闭文件
  rep_->file.flush();
  rep_->file.close();
  
  if (rep_->file.fail()) {
    return Status::IOError("关闭文件失败");
  }
  
  // 原子重命名：从临时文件到正式文件
  if (std::rename(rep_->temp_filename.c_str(), rep_->filename.c_str()) != 0) {
    return Status::IOError("重命名文件失败");
  }
  
  rep_->finished = true;
  
  if (file_size != nullptr) {
    *file_size = rep_->offset;
  }
  
  return Status::OK();
}

void SSTableBuilder::Abandon() {
  if (rep_ != nullptr) {
    if (rep_->file.is_open()) {
      rep_->file.close();
    }
    
    // 删除临时文件
    std::remove(rep_->temp_filename.c_str());
    rep_->finished = true;
  }
}

uint64_t SSTableBuilder::FileSize() const {
  return rep_ != nullptr ? rep_->offset : 0;
}

uint64_t SSTableBuilder::NumEntries() const {
  return rep_ != nullptr ? rep_->num_entries : 0;
}

// 辅助函数实现
std::string SSTableFileName(uint64_t seq_number) {
  std::ostringstream oss;
  oss << std::setw(6) << std::setfill('0') << seq_number << ".sst";
  return oss.str();
}

bool ParseSSTableFileName(const std::string& filename, uint64_t* seq_number) {
  if (filename.length() != 10 || filename.substr(filename.length() - 4) != ".sst") {
    return false;
  }
  
  std::string number_part = filename.substr(0, 6);
  for (char c : number_part) {
    if (c < '0' || c > '9') {
      return false;
    }
  }
  
  *seq_number = std::stoull(number_part);
  return true;
}

}  // namespace kvstore
