#include "kvstore/memtable.h"
#include "kvstore/comparator.h"
#include <cstring>
#include <cassert>
#include <cstdint>

namespace kvstore {

// Helper functions for varint encoding/decoding
static void PutVarint32(std::string* dst, uint32_t v) {
  char buf[5];
  char* ptr = buf;
  static const int B = 128;
  if (v < (1 << 7)) {
    *(ptr++) = v;
  } else if (v < (1 << 14)) {
    *(ptr++) = v | B;
    *(ptr++) = v >> 7;
  } else if (v < (1 << 21)) {
    *(ptr++) = v | B;
    *(ptr++) = (v >> 7) | B;
    *(ptr++) = v >> 14;
  } else if (v < (1 << 28)) {
    *(ptr++) = v | B;
    *(ptr++) = (v >> 7) | B;
    *(ptr++) = (v >> 14) | B;
    *(ptr++) = v >> 21;
  } else {
    *(ptr++) = v | B;
    *(ptr++) = (v >> 7) | B;
    *(ptr++) = (v >> 14) | B;
    *(ptr++) = (v >> 21) | B;
    *(ptr++) = v >> 28;
  }
  dst->append(buf, ptr - buf);
}

static const char* GetVarint32Ptr(const char* p, const char* limit, uint32_t* value) {
  if (p < limit) {
    uint32_t result = *(reinterpret_cast<const uint8_t*>(p));
    if ((result & 128) == 0) {
      *value = result;
      return p + 1;
    }
  }
  
  // Fallback for multi-byte varints
  uint32_t result = 0;
  for (uint32_t shift = 0; shift <= 28 && p < limit; shift += 7) {
    uint32_t byte = *(reinterpret_cast<const uint8_t*>(p));
    p++;
    if (byte & 128) {
      result |= ((byte & 127) << shift);
    } else {
      result |= (byte << shift);
      *value = result;
      return p;
    }
  }
  return nullptr;
}

static Slice GetLengthPrefixedSlice(const char* data) {
  uint32_t len;
  const char* p = GetVarint32Ptr(data, data + 5, &len);
  return Slice(p, len);
}

// Encode a suitable internal key target for "target" and return it.
static const char* EncodeKey(std::string* scratch, const Slice& target) {
  scratch->clear();
  PutVarint32(scratch, static_cast<uint32_t>(target.size()));
  scratch->append(target.data(), target.size());
  return scratch->data();
}

// MemTableIterator implementation
class MemTableIterator {
 public:
  explicit MemTableIterator(SkipList<const char*, MemTable::KeyComparator>* table) : iter_(table) {}

  bool Valid() const { return iter_.Valid(); }
  
  void Seek(const Slice& k) {
    std::string memkey;
    iter_.Seek(EncodeKey(&memkey, k));
  }
  
  void SeekToFirst() { iter_.SeekToFirst(); }
  void SeekToLast() { iter_.SeekToLast(); }
  void Next() { iter_.Next(); }
  void Prev() { iter_.Prev(); }
  
  Slice key() const {
    return GetLengthPrefixedSlice(iter_.key());
  }
  
  Slice value() const {
    Slice key_slice = GetLengthPrefixedSlice(iter_.key());
    return GetLengthPrefixedSlice(key_slice.data() + key_slice.size());
  }

 private:
  SkipList<const char*, MemTable::KeyComparator>::Iterator iter_;
};

// MemTable::Iterator implementation
MemTable::Iterator::Iterator(MemTable* mem) 
    : iter_(new MemTableIterator(&mem->table_)) {}

MemTable::Iterator::~Iterator() { 
  delete static_cast<MemTableIterator*>(iter_); 
}

bool MemTable::Iterator::Valid() const {
  return static_cast<MemTableIterator*>(iter_)->Valid();
}

void MemTable::Iterator::Seek(const Slice& k) {
  static_cast<MemTableIterator*>(iter_)->Seek(k);
}

void MemTable::Iterator::SeekToFirst() {
  static_cast<MemTableIterator*>(iter_)->SeekToFirst();
}

void MemTable::Iterator::SeekToLast() {
  static_cast<MemTableIterator*>(iter_)->SeekToLast();
}

void MemTable::Iterator::Next() {
  static_cast<MemTableIterator*>(iter_)->Next();
}

void MemTable::Iterator::Prev() {
  static_cast<MemTableIterator*>(iter_)->Prev();
}

Slice MemTable::Iterator::key() const {
  return static_cast<MemTableIterator*>(iter_)->key();
}

Slice MemTable::Iterator::value() const {
  return static_cast<MemTableIterator*>(iter_)->value();
}

// LookupKey class
class LookupKey {
 private:
  char* start_;
  const char* kstart_;
  const char* end_;
  char space_[200];  // Avoid allocation for short keys

 public:
  LookupKey(const Slice& user_key) {
    size_t usize = user_key.size();
    size_t needed = usize + 13;  // Conservative estimate
    char* dst;
    if (needed <= 200) {
      dst = space_;
    } else {
      dst = new char[needed];
    }
    start_ = dst;
    
    // Use proper varint encoding for key length
    std::string temp;
    PutVarint32(&temp, static_cast<uint32_t>(usize));
    std::memcpy(dst, temp.data(), temp.size());
    dst += temp.size();
    
    kstart_ = dst;
    std::memcpy(dst, user_key.data(), usize);
    dst += usize;
    end_ = dst;
  }

  ~LookupKey() {
    if (start_ != space_) {
      delete[] start_;
    }
  }

  // Return a key suitable for lookup in a MemTable.
  Slice memtable_key() const { return Slice(start_, end_ - start_); }

  // Return the user key
  Slice user_key() const { return Slice(kstart_, end_ - kstart_); }

 private:
  LookupKey(const LookupKey&) = delete;
  LookupKey& operator=(const LookupKey&) = delete;
};

// MemTable implementation
MemTable::MemTable(const Comparator* cmp) : comparator_(cmp), refs_(0), table_(comparator_) {}

MemTable::~MemTable() { assert(refs_ == 0); }

size_t MemTable::ApproximateMemoryUsage() {
  // 估算MemTable的内存使用量
  // 包括：SkipList节点 + 存储的key-value数据
  
  // 基础对象大小
  size_t usage = sizeof(MemTable);
  
  // SkipList节点的估算：
  // - 每个节点包含指针数组（平均高度约4层）
  // - 每个指针8字节（64位系统）
  // - 节点本身的开销
  const size_t average_node_height = 4;
  const size_t node_overhead = sizeof(void*) * average_node_height + 32; // 指针 + 其他开销
  
  // 遍历SkipList统计数据大小
  auto iter = NewIterator();
  size_t entry_count = 0;
  size_t data_size = 0;
  
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    // 每个条目的数据大小：varint头 + key + varint头 + value
    Slice key = iter->key();
    Slice value = iter->value();
    
    // varint长度计算（简化估算：小于128的用1字节，其他用2-5字节）
    size_t key_varint_len = key.size() < 128 ? 1 : (key.size() < 16384 ? 2 : 5);
    size_t value_varint_len = value.size() < 128 ? 1 : (value.size() < 16384 ? 2 : 5);
    
    data_size += key_varint_len + key.size() + value_varint_len + value.size();
    entry_count++;
  }
  
  delete iter;
  
  // 总内存使用量 = 基础大小 + 节点开销 + 数据大小
  usage += entry_count * node_overhead + data_size;
  
  return usage;
}

int MemTable::KeyComparator::operator()(const char* aptr, const char* bptr) const {
  // Internal keys are encoded as length-prefixed strings.
  Slice a = GetLengthPrefixedSlice(aptr);
  Slice b = GetLengthPrefixedSlice(bptr);
  return comparator->Compare(a, b);
}

MemTable::Iterator* MemTable::NewIterator() { 
  return new Iterator(this); 
}

void MemTable::Add(const Slice& key, const Slice& value) {
  // Format of an entry is concatenation of:
  //  key_size     : varint32 of key.size()
  //  key bytes    : char[key_size]
  //  value_size   : varint32 of value.size()
  //  value bytes  : char[value_size]
  size_t key_size = key.size();
  size_t val_size = value.size();
  size_t internal_key_size = key_size + val_size + 10;  // Conservative estimate

  char* buf = new char[internal_key_size];
  char* p = buf;
  
  // Use proper varint encoding for key
  std::string temp;
  PutVarint32(&temp, static_cast<uint32_t>(key_size));
  std::memcpy(p, temp.data(), temp.size());
  p += temp.size();
  std::memcpy(p, key.data(), key_size);
  p += key_size;
  
  // Use proper varint encoding for value
  temp.clear();
  PutVarint32(&temp, static_cast<uint32_t>(val_size));
  std::memcpy(p, temp.data(), temp.size());
  p += temp.size();
  std::memcpy(p, value.data(), val_size);
  p += val_size;
  
  assert(p <= buf + internal_key_size);
  table_.Insert(buf);
}

bool MemTable::Get(const Slice& key, std::string* value, Status* /* s */) {
  LookupKey lkey(key);
  SkipList<const char*, KeyComparator>::Iterator iter(&table_);
  iter.Seek(lkey.memtable_key().data());
  if (iter.Valid()) {
    // entry format is:
    //    key_length  varint32
    //    key         char[key_length]  
    //    value_length varint32
    //    value       char[value_length]
    const char* entry = iter.key();
    
    // Decode key length and key
    uint32_t key_length;
    const char* key_ptr = GetVarint32Ptr(entry, entry + 5, &key_length);
    
    if (comparator_.comparator->Compare(Slice(key_ptr, key_length), key) == 0) {
      // Correct user key
      const char* value_ptr = key_ptr + key_length;
      uint32_t value_length;
      const char* value_start = GetVarint32Ptr(value_ptr, value_ptr + 5, &value_length);
      value->assign(value_start, value_length);
      return true;
    }
  }
  return false;
}

}  // namespace kvstore