
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/mnt/hgfs/my_share/kvstore/src/comparator.cc" "CMakeFiles/kvstore.dir/src/comparator.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/comparator.cc.o.d"
  "/mnt/hgfs/my_share/kvstore/src/db.cc" "CMakeFiles/kvstore.dir/src/db.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/db.cc.o.d"
  "/mnt/hgfs/my_share/kvstore/src/memtable.cc" "CMakeFiles/kvstore.dir/src/memtable.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/memtable.cc.o.d"
  "/mnt/hgfs/my_share/kvstore/src/slice.cc" "CMakeFiles/kvstore.dir/src/slice.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/slice.cc.o.d"
  "/mnt/hgfs/my_share/kvstore/src/sstable.cc" "CMakeFiles/kvstore.dir/src/sstable.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/sstable.cc.o.d"
  "/mnt/hgfs/my_share/kvstore/src/sstable_builder.cc" "CMakeFiles/kvstore.dir/src/sstable_builder.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/sstable_builder.cc.o.d"
  "/mnt/hgfs/my_share/kvstore/src/status.cc" "CMakeFiles/kvstore.dir/src/status.cc.o" "gcc" "CMakeFiles/kvstore.dir/src/status.cc.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
