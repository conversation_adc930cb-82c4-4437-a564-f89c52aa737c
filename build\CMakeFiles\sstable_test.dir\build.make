# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/hgfs/my_share/kvstore

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/hgfs/my_share/kvstore/build

# Include any dependencies generated for this target.
include CMakeFiles/sstable_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sstable_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sstable_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sstable_test.dir/flags.make

CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o: CMakeFiles/sstable_test.dir/flags.make
CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o: /mnt/hgfs/my_share/kvstore/tests/sstable_test.cc
CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o: CMakeFiles/sstable_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o -MF CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o.d -o CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o -c /mnt/hgfs/my_share/kvstore/tests/sstable_test.cc

CMakeFiles/sstable_test.dir/tests/sstable_test.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/sstable_test.dir/tests/sstable_test.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/tests/sstable_test.cc > CMakeFiles/sstable_test.dir/tests/sstable_test.cc.i

CMakeFiles/sstable_test.dir/tests/sstable_test.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/sstable_test.dir/tests/sstable_test.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/tests/sstable_test.cc -o CMakeFiles/sstable_test.dir/tests/sstable_test.cc.s

# Object files for target sstable_test
sstable_test_OBJECTS = \
"CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o"

# External object files for target sstable_test
sstable_test_EXTERNAL_OBJECTS =

sstable_test: CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o
sstable_test: CMakeFiles/sstable_test.dir/build.make
sstable_test: libkvstore.a
sstable_test: /usr/lib/x86_64-linux-gnu/libgtest.a
sstable_test: /usr/lib/x86_64-linux-gnu/libgtest_main.a
sstable_test: /usr/lib/x86_64-linux-gnu/libgtest.a
sstable_test: CMakeFiles/sstable_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable sstable_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sstable_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sstable_test.dir/build: sstable_test
.PHONY : CMakeFiles/sstable_test.dir/build

CMakeFiles/sstable_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sstable_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sstable_test.dir/clean

CMakeFiles/sstable_test.dir/depend:
	cd /mnt/hgfs/my_share/kvstore/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/hgfs/my_share/kvstore /mnt/hgfs/my_share/kvstore /mnt/hgfs/my_share/kvstore/build /mnt/hgfs/my_share/kvstore/build /mnt/hgfs/my_share/kvstore/build/CMakeFiles/sstable_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/sstable_test.dir/depend

