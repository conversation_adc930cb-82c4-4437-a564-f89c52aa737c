Start testing: Sep 08 15:06 CST
----------------------------------------------------------
1/4 Testing: slice_test
1/4 Test: slice_test
Command: "/mnt/hgfs/my_share/kvstore/build/slice_test"
Directory: /mnt/hgfs/my_share/kvstore/build
"slice_test" start time: Sep 08 15:06 CST
Output:
----------------------------------------------------------
Running main() from ./googletest/src/gtest_main.cc
[==========] Running 10 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 10 tests from SliceTest
[ RUN      ] SliceTest.Construction
[       OK ] SliceTest.Construction (0 ms)
[ RUN      ] SliceTest.CopySemantics
[       OK ] SliceTest.CopySemantics (0 ms)
[ RUN      ] SliceTest.Comparison
[       OK ] SliceTest.Comparison (0 ms)
[ RUN      ] SliceTest.StartsWith
[       OK ] SliceTest.StartsWith (0 ms)
[ RUN      ] SliceTest.ArrayAccess
[       OK ] SliceTest.ArrayAccess (0 ms)
[ RUN      ] SliceTest.RemovePrefix
[       OK ] SliceTest.RemovePrefix (0 ms)
[ RUN      ] SliceTest.Clear
[       OK ] SliceTest.Clear (0 ms)
[ RUN      ] SliceTest.BinaryData
[       OK ] SliceTest.BinaryData (0 ms)
[ RUN      ] SliceTest.EdgeCases
[       OK ] SliceTest.EdgeCases (0 ms)
[ RUN      ] SliceTest.LexicographicOrdering
[       OK ] SliceTest.LexicographicOrdering (0 ms)
[----------] 10 tests from SliceTest (0 ms total)

[----------] Global test environment tear-down
[==========] 10 tests from 1 test suite ran. (0 ms total)
[  PASSED  ] 10 tests.
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"slice_test" end time: Sep 08 15:06 CST
"slice_test" time elapsed: 00:00:00
----------------------------------------------------------

2/4 Testing: skiplist_test
2/4 Test: skiplist_test
Command: "/mnt/hgfs/my_share/kvstore/build/skiplist_test"
Directory: /mnt/hgfs/my_share/kvstore/build
"skiplist_test" start time: Sep 08 15:06 CST
Output:
----------------------------------------------------------
Running main() from ./googletest/src/gtest_main.cc
[==========] Running 10 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 10 tests from SkipListTest
[ RUN      ] SkipListTest.BasicOperations
[       OK ] SkipListTest.BasicOperations (0 ms)
[ RUN      ] SkipListTest.Iterator
[       OK ] SkipListTest.Iterator (0 ms)
[ RUN      ] SkipListTest.IteratorSeek
[       OK ] SkipListTest.IteratorSeek (0 ms)
[ RUN      ] SkipListTest.BackwardIteration
[       OK ] SkipListTest.BackwardIteration (0 ms)
[ RUN      ] SkipListTest.EmptyList
[       OK ] SkipListTest.EmptyList (0 ms)
[ RUN      ] SkipListTest.SingleElement
[       OK ] SkipListTest.SingleElement (0 ms)
[ RUN      ] SkipListTest.LargeDataSet
[       OK ] SkipListTest.LargeDataSet (1 ms)
[ RUN      ] SkipListTest.ConcurrentInsertion
[       OK ] SkipListTest.ConcurrentInsertion (25 ms)
[ RUN      ] SkipListTest.ConcurrentReadWrite
[       OK ] SkipListTest.ConcurrentReadWrite (24 ms)
[ RUN      ] SkipListTest.CustomComparator
[       OK ] SkipListTest.CustomComparator (0 ms)
[----------] 10 tests from SkipListTest (52 ms total)

[----------] Global test environment tear-down
[==========] 10 tests from 1 test suite ran. (52 ms total)
[  PASSED  ] 10 tests.
<end of output>
Test time =   0.06 sec
----------------------------------------------------------
Test Passed.
"skiplist_test" end time: Sep 08 15:06 CST
"skiplist_test" time elapsed: 00:00:00
----------------------------------------------------------

3/4 Testing: memtable_test
3/4 Test: memtable_test
Command: "/mnt/hgfs/my_share/kvstore/build/memtable_test"
Directory: /mnt/hgfs/my_share/kvstore/build
"memtable_test" start time: Sep 08 15:06 CST
Output:
----------------------------------------------------------
Running main() from ./googletest/src/gtest_main.cc
[==========] Running 12 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 12 tests from MemTableTest
[ RUN      ] MemTableTest.BasicOperations
[       OK ] MemTableTest.BasicOperations (0 ms)
[ RUN      ] MemTableTest.MultipleKeyValues
[       OK ] MemTableTest.MultipleKeyValues (0 ms)
[ RUN      ] MemTableTest.Iterator
[       OK ] MemTableTest.Iterator (0 ms)
[ RUN      ] MemTableTest.IteratorSeek
[       OK ] MemTableTest.IteratorSeek (0 ms)
[ RUN      ] MemTableTest.BackwardIteration
[       OK ] MemTableTest.BackwardIteration (0 ms)
[ RUN      ] MemTableTest.EmptyIterator
[       OK ] MemTableTest.EmptyIterator (0 ms)
[ RUN      ] MemTableTest.ReferenceCounting
[       OK ] MemTableTest.ReferenceCounting (0 ms)
[ RUN      ] MemTableTest.BinaryData
[       OK ] MemTableTest.BinaryData (0 ms)
[ RUN      ] MemTableTest.LargeData
[       OK ] MemTableTest.LargeData (0 ms)
[ RUN      ] MemTableTest.ManyEntries
[       OK ] MemTableTest.ManyEntries (2 ms)
[ RUN      ] MemTableTest.ConcurrentAccess
[       OK ] MemTableTest.ConcurrentAccess (1 ms)
[ RUN      ] MemTableTest.ConcurrentReadWrite
[       OK ] MemTableTest.ConcurrentReadWrite (26 ms)
[----------] 12 tests from MemTableTest (30 ms total)

[----------] Global test environment tear-down
[==========] 12 tests from 1 test suite ran. (30 ms total)
[  PASSED  ] 12 tests.
<end of output>
Test time =   0.04 sec
----------------------------------------------------------
Test Passed.
"memtable_test" end time: Sep 08 15:06 CST
"memtable_test" time elapsed: 00:00:00
----------------------------------------------------------

4/4 Testing: sstable_test
4/4 Test: sstable_test
Command: "/mnt/hgfs/my_share/kvstore/build/sstable_test"
Directory: /mnt/hgfs/my_share/kvstore/build
"sstable_test" start time: Sep 08 15:06 CST
Output:
----------------------------------------------------------
Running main() from ./googletest/src/gtest_main.cc
[==========] Running 8 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 8 tests from SSTableTest
[ RUN      ] SSTableTest.BuilderBasicOperations
[       OK ] SSTableTest.BuilderBasicOperations (6 ms)
[ RUN      ] SSTableTest.BuilderKeyOrdering
[       OK ] SSTableTest.BuilderKeyOrdering (3 ms)
[ RUN      ] SSTableTest.SSTableRead
/mnt/hgfs/my_share/kvstore/tests/sstable_test.cc:96: Failure
Expected equality of these values:
  3u
    Which is: 3
  sstable->NumEntries()
    Which is: 1703205355008802

/mnt/hgfs/my_share/kvstore/tests/sstable_test.cc:109: Failure
Value of: sstable->Get("cherry", &value).ok()
  Actual: false
Expected: true

[  FAILED  ] SSTableTest.SSTableRead (5 ms)
[ RUN      ] SSTableTest.IteratorTraversal
/mnt/hgfs/my_share/kvstore/tests/sstable_test.cc:134: Failure
Value of: SSTable::Open(filename, comparator_, &sstable).ok()
  Actual: false
Expected: true

[  FAILED  ] SSTableTest.IteratorTraversal (4 ms)
[ RUN      ] SSTableTest.EmptySSTable
/mnt/hgfs/my_share/kvstore/tests/sstable_test.cc:181: Failure
Value of: SSTable::Open(filename, comparator_, &sstable).ok()
  Actual: false
Expected: true

[  FAILED  ] SSTableTest.EmptySSTable (4 ms)
[ RUN      ] SSTableTest.LargeDataSSTable
/mnt/hgfs/my_share/kvstore/tests/sstable_test.cc:209: Failure
Value of: builder.Add(key, value).ok()
  Actual: false
Expected: true

[  FAILED  ] SSTableTest.LargeDataSSTable (3 ms)
[ RUN      ] SSTableTest.DatabaseFlushIntegration
/mnt/hgfs/my_share/kvstore/tests/sstable_test.cc:246: Failure
Value of: db->Put(key, value).ok()
  Actual: false
Expected: true

[  FAILED  ] SSTableTest.DatabaseFlushIntegration (14 ms)
[ RUN      ] SSTableTest.FileNameParsing
[       OK ] SSTableTest.FileNameParsing (2 ms)
[----------] 8 tests from SSTableTest (45 ms total)

[----------] Global test environment tear-down
[==========] 8 tests from 1 test suite ran. (45 ms total)
[  PASSED  ] 3 tests.
[  FAILED  ] 5 tests, listed below:
[  FAILED  ] SSTableTest.SSTableRead
[  FAILED  ] SSTableTest.IteratorTraversal
[  FAILED  ] SSTableTest.EmptySSTable
[  FAILED  ] SSTableTest.LargeDataSSTable
[  FAILED  ] SSTableTest.DatabaseFlushIntegration

 5 FAILED TESTS
<end of output>
Test time =   0.05 sec
----------------------------------------------------------
Test Failed.
"sstable_test" end time: Sep 08 15:06 CST
"sstable_test" time elapsed: 00:00:00
----------------------------------------------------------

End testing: Sep 08 15:06 CST
