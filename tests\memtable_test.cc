#include "kvstore/memtable.h"
#include "kvstore/comparator.h"
#include "kvstore/status.h"
#include <gtest/gtest.h>
#include <thread>
#include <vector>
#include <random>
#include <map>

using namespace kvstore;

class MemTableTest : public ::testing::Test {
 protected:
  void SetUp() override {
    comparator_ = BytewiseComparator();
    memtable_ = new MemTable(comparator_);
    memtable_->Ref();
  }
  
  void TearDown() override {
    memtable_->Unref();
  }
  
  const Comparator* comparator_;
  MemTable* memtable_;
};

// Test basic Add and Get operations
TEST_F(MemTableTest, BasicOperations) {
  Status s;
  std::string value;
  
  // Test Get on empty memtable
  EXPECT_FALSE(memtable_->Get(Slice("key1"), &value, &s));
  
  // Add a key-value pair
  memtable_->Add(Slice("key1"), Slice("value1"));
  
  // Test successful Get
  EXPECT_TRUE(memtable_->Get(Slice("key1"), &value, &s));
  EXPECT_EQ("value1", value);
  EXPECT_TRUE(s.ok());
  
  // Test Get for non-existent key
  EXPECT_FALSE(memtable_->Get(Slice("key2"), &value, &s));
}

// Test multiple key-value pairs
TEST_F(MemTableTest, MultipleKeyValues) {
  Status s;
  std::string value;
  
  // Add multiple key-value pairs
  memtable_->Add(Slice("apple"), Slice("red"));
  memtable_->Add(Slice("banana"), Slice("yellow"));
  memtable_->Add(Slice("cherry"), Slice("red"));
  memtable_->Add(Slice("date"), Slice("brown"));
  
  // Test all keys
  EXPECT_TRUE(memtable_->Get(Slice("apple"), &value, &s));
  EXPECT_EQ("red", value);
  
  EXPECT_TRUE(memtable_->Get(Slice("banana"), &value, &s));
  EXPECT_EQ("yellow", value);
  
  EXPECT_TRUE(memtable_->Get(Slice("cherry"), &value, &s));
  EXPECT_EQ("red", value);
  
  EXPECT_TRUE(memtable_->Get(Slice("date"), &value, &s));
  EXPECT_EQ("brown", value);
  
  // Test non-existent key
  EXPECT_FALSE(memtable_->Get(Slice("elderberry"), &value, &s));
}

// Test iterator functionality
TEST_F(MemTableTest, Iterator) {
  // Add data in non-sorted order
  memtable_->Add(Slice("d"), Slice("4"));
  memtable_->Add(Slice("b"), Slice("2"));
  memtable_->Add(Slice("a"), Slice("1"));
  memtable_->Add(Slice("c"), Slice("3"));
  
  MemTable::Iterator* iter = memtable_->NewIterator();
  
  // Test forward iteration
  std::vector<std::pair<std::string, std::string>> result;
  iter->SeekToFirst();
  while (iter->Valid()) {
    result.emplace_back(iter->key().ToString(), iter->value().ToString());
    iter->Next();
  }
  
  std::vector<std::pair<std::string, std::string>> expected = {
    {"a", "1"}, {"b", "2"}, {"c", "3"}, {"d", "4"}
  };
  EXPECT_EQ(expected, result);
  
  delete iter;
}

// Test iterator seek operations
TEST_F(MemTableTest, IteratorSeek) {
  // Add test data
  memtable_->Add(Slice("b"), Slice("2"));
  memtable_->Add(Slice("d"), Slice("4"));
  memtable_->Add(Slice("f"), Slice("6"));
  memtable_->Add(Slice("h"), Slice("8"));
  
  MemTable::Iterator* iter = memtable_->NewIterator();
  
  // Seek to existing key
  iter->Seek(Slice("d"));
  EXPECT_TRUE(iter->Valid());
  EXPECT_EQ("d", iter->key().ToString());
  EXPECT_EQ("4", iter->value().ToString());
  
  // Seek to non-existing key (should find next larger)
  iter->Seek(Slice("c"));
  EXPECT_TRUE(iter->Valid());
  EXPECT_EQ("d", iter->key().ToString());
  
  // Seek to key smaller than all
  iter->Seek(Slice("a"));
  EXPECT_TRUE(iter->Valid());
  EXPECT_EQ("b", iter->key().ToString());
  
  // Seek to key larger than all
  iter->Seek(Slice("z"));
  EXPECT_FALSE(iter->Valid());
  
  delete iter;
}

// Test backward iteration
TEST_F(MemTableTest, BackwardIteration) {
  memtable_->Add(Slice("a"), Slice("1"));
  memtable_->Add(Slice("c"), Slice("3"));
  memtable_->Add(Slice("e"), Slice("5"));
  memtable_->Add(Slice("g"), Slice("7"));
  
  MemTable::Iterator* iter = memtable_->NewIterator();
  
  // Test backward iteration
  std::vector<std::pair<std::string, std::string>> result;
  iter->SeekToLast();
  while (iter->Valid()) {
    result.emplace_back(iter->key().ToString(), iter->value().ToString());
    iter->Prev();
  }
  
  std::vector<std::pair<std::string, std::string>> expected = {
    {"g", "7"}, {"e", "5"}, {"c", "3"}, {"a", "1"}
  };
  EXPECT_EQ(expected, result);
  
  delete iter;
}

// Test empty memtable iterator
TEST_F(MemTableTest, EmptyIterator) {
  MemTable::Iterator* iter = memtable_->NewIterator();
  
  iter->SeekToFirst();
  EXPECT_FALSE(iter->Valid());
  
  iter->SeekToLast();
  EXPECT_FALSE(iter->Valid());
  
  iter->Seek(Slice("any"));
  EXPECT_FALSE(iter->Valid());
  
  delete iter;
}

// Test reference counting
TEST_F(MemTableTest, ReferenceCounting) {
  MemTable* mt = new MemTable(comparator_);
  
  // Initial reference count should allow Unref to delete
  mt->Ref();  // ref count = 1
  mt->Ref();  // ref count = 2
  
  mt->Add(Slice("test"), Slice("value"));
  
  mt->Unref();  // ref count = 1, should not delete
  
  // Should still be usable
  std::string value;
  Status s;
  EXPECT_TRUE(mt->Get(Slice("test"), &value, &s));
  EXPECT_EQ("value", value);
  
  mt->Unref();  // ref count = 0, should delete automatically
  // mt is now invalid, don't use it
}

// Test binary data (keys and values with null bytes)
TEST_F(MemTableTest, BinaryData) {
  Status s;
  std::string value;
  
  // Create binary key and value with null bytes
  std::string binary_key = std::string("key\0with\0nulls", 14);
  std::string binary_value = std::string("value\0with\0nulls", 16);
  
  memtable_->Add(Slice(binary_key), Slice(binary_value));
  
  EXPECT_TRUE(memtable_->Get(Slice(binary_key), &value, &s));
  EXPECT_EQ(binary_value, value);
  EXPECT_TRUE(s.ok());
  
  // Verify null bytes are preserved
  EXPECT_EQ(16, value.size());
  EXPECT_EQ('\0', value[5]);
  EXPECT_EQ('\0', value[10]);
}

// Test large keys and values
TEST_F(MemTableTest, LargeData) {
  Status s;
  std::string value;
  
  // Create large key and value
  std::string large_key(1000, 'k');
  std::string large_value(10000, 'v');
  
  memtable_->Add(Slice(large_key), Slice(large_value));
  
  EXPECT_TRUE(memtable_->Get(Slice(large_key), &value, &s));
  EXPECT_EQ(large_value, value);
  EXPECT_EQ(10000, value.size());
}

// Test many entries
TEST_F(MemTableTest, ManyEntries) {
  const int num_entries = 1000;
  Status s;
  std::string value;
  
  // Add many entries
  std::map<std::string, std::string> expected;
  for (int i = 0; i < num_entries; ++i) {
    std::string key = "key_" + std::to_string(i);
    std::string val = "value_" + std::to_string(i);
    memtable_->Add(Slice(key), Slice(val));
    expected[key] = val;
  }
  
  // Verify all entries
  for (const auto& kv : expected) {
    EXPECT_TRUE(memtable_->Get(Slice(kv.first), &value, &s));
    EXPECT_EQ(kv.second, value);
  }
  
  // Verify sorted iteration
  MemTable::Iterator* iter = memtable_->NewIterator();
  iter->SeekToFirst();
  
  auto it = expected.begin();
  while (iter->Valid() && it != expected.end()) {
    EXPECT_EQ(it->first, iter->key().ToString());
    EXPECT_EQ(it->second, iter->value().ToString());
    iter->Next();
    ++it;
  }
  
  EXPECT_FALSE(iter->Valid());
  EXPECT_EQ(expected.end(), it);
  
  delete iter;
}

// Test concurrent access (basic thread safety)
TEST_F(MemTableTest, ConcurrentAccess) {
  const int num_threads = 4;
  const int entries_per_thread = 100;
  
  std::vector<std::thread> threads;
  std::vector<std::map<std::string, std::string>> thread_data(num_threads);
  
  // Prepare data for each thread
  for (int t = 0; t < num_threads; ++t) {
    for (int i = 0; i < entries_per_thread; ++i) {
      std::string key = "thread_" + std::to_string(t) + "_key_" + std::to_string(i);
      std::string value = "thread_" + std::to_string(t) + "_value_" + std::to_string(i);
      thread_data[t][key] = value;
    }
  }
  
  // Launch threads to add data concurrently
  for (int t = 0; t < num_threads; ++t) {
    threads.emplace_back([this, &thread_data, t]() {
      for (const auto& kv : thread_data[t]) {
        memtable_->Add(Slice(kv.first), Slice(kv.second));
      }
    });
  }
  
  // Wait for all threads
  for (auto& thread : threads) {
    thread.join();
  }
  
  // Verify all data is present
  Status s;
  std::string value;
  for (int t = 0; t < num_threads; ++t) {
    for (const auto& kv : thread_data[t]) {
      EXPECT_TRUE(memtable_->Get(Slice(kv.first), &value, &s));
      EXPECT_EQ(kv.second, value);
    }
  }
}

// Test concurrent read-write
TEST_F(MemTableTest, ConcurrentReadWrite) {
  const int num_writers = 2;
  const int num_readers = 2;
  const int writes_per_thread = 50;
  
  std::atomic<bool> stop_readers{false};
  std::vector<std::thread> threads;
  std::vector<std::map<std::string, std::string>> writer_data(num_writers);
  
  // Prepare writer data
  for (int w = 0; w < num_writers; ++w) {
    for (int i = 0; i < writes_per_thread; ++i) {
      std::string key = "writer_" + std::to_string(w) + "_" + std::to_string(i);
      std::string value = "value_" + std::to_string(w) + "_" + std::to_string(i);
      writer_data[w][key] = value;
    }
  }
  
  // Launch writer threads
  for (int w = 0; w < num_writers; ++w) {
    threads.emplace_back([this, &writer_data, w]() {
      for (const auto& kv : writer_data[w]) {
        memtable_->Add(Slice(kv.first), Slice(kv.second));
        std::this_thread::sleep_for(std::chrono::microseconds(1));
      }
    });
  }
  
  // Launch reader threads
  std::vector<int> read_counts(num_readers, 0);
  for (int r = 0; r < num_readers; ++r) {
    threads.emplace_back([this, &stop_readers, &read_counts, r]() {
      Status s;
      std::string value;
      while (!stop_readers.load()) {
        MemTable::Iterator* iter = memtable_->NewIterator();
        iter->SeekToFirst();
        while (iter->Valid()) {
          read_counts[r]++;
          iter->Next();
        }
        delete iter;
        std::this_thread::sleep_for(std::chrono::microseconds(1));
      }
    });
  }
  
  // Wait for writers to complete
  for (int i = 0; i < num_writers; ++i) {
    threads[i].join();
  }
  
  // Stop readers
  stop_readers.store(true);
  for (int i = num_writers; i < num_writers + num_readers; ++i) {
    threads[i].join();
  }
  
  // Verify final state
  Status s;
  std::string value;
  for (int w = 0; w < num_writers; ++w) {
    for (const auto& kv : writer_data[w]) {
      EXPECT_TRUE(memtable_->Get(Slice(kv.first), &value, &s));
      EXPECT_EQ(kv.second, value);
    }
  }
  
  // Verify readers performed some reads
  for (int r = 0; r < num_readers; ++r) {
    EXPECT_GT(read_counts[r], 0) << "Reader " << r << " didn't perform any reads";
  }
}
