#include "kvstore/skiplist.h"
#include "kvstore/comparator.h"
#include "kvstore/slice.h"
#include <gtest/gtest.h>
#include <thread>
#include <vector>
#include <random>
#include <set>
#include <algorithm>

using namespace kvstore;

// Custom comparator for std::string that converts to Slice
struct StringComparator {
  const Comparator* comp;
  
  StringComparator() : comp(nullptr) {}
  explicit StringComparator(const Comparator* c) : comp(c) {}
  
  int operator()(const std::string& a, const std::string& b) const {
    if (comp == nullptr) {
      // Fallback to basic string comparison
      if (a < b) return -1;
      if (a > b) return 1;
      return 0;
    }
    return comp->Compare(Slice(a), Slice(b));
  }
};

class SkipListTest : public ::testing::Test {
 protected:
  typedef SkipList<std::string, StringComparator> StringSkipList;
  
  SkipListTest() : comparator_(nullptr), string_comp_(), list_(nullptr) {}
  
  void SetUp() override {
    comparator_ = BytewiseComparator();
    string_comp_ = StringComparator(comparator_);
    list_ = new StringSkipList(string_comp_);
  }
  
  void TearDown() override {
    delete list_;
  }
  
  const Comparator* comparator_;
  StringComparator string_comp_;
  StringSkipList* list_;
};

// Test basic insert and contains operations
TEST_F(SkipListTest, BasicOperations) {
  EXPECT_FALSE(list_->Contains("key1"));
  
  list_->Insert("key1");
  EXPECT_TRUE(list_->Contains("key1"));
  EXPECT_FALSE(list_->Contains("key2"));
  
  list_->Insert("key2");
  EXPECT_TRUE(list_->Contains("key1"));
  EXPECT_TRUE(list_->Contains("key2"));
  EXPECT_FALSE(list_->Contains("key3"));
}

// Test iterator functionality
TEST_F(SkipListTest, Iterator) {
  // Insert in reverse order to test sorting
  std::vector<std::string> keys = {"key3", "key1", "key4", "key2"};
  for (const auto& key : keys) {
    list_->Insert(key);
  }
  
  // Test forward iteration
  StringSkipList::Iterator iter(list_);
  iter.SeekToFirst();
  
  std::vector<std::string> result;
  while (iter.Valid()) {
    result.push_back(iter.key());
    iter.Next();
  }
  
  std::vector<std::string> expected = {"key1", "key2", "key3", "key4"};
  EXPECT_EQ(expected, result);
}

// Test iterator seek operations
TEST_F(SkipListTest, IteratorSeek) {
  std::vector<std::string> keys = {"b", "d", "f", "h", "j"};
  for (const auto& key : keys) {
    list_->Insert(key);
  }
  
  StringSkipList::Iterator iter(list_);
  
  // Test seeking to existing keys
  iter.Seek("d");
  EXPECT_TRUE(iter.Valid());
  EXPECT_EQ("d", iter.key());
  
  // Test seeking to non-existing key (should find next larger)
  iter.Seek("c");
  EXPECT_TRUE(iter.Valid());
  EXPECT_EQ("d", iter.key());
  
  // Test seeking to key larger than all
  iter.Seek("z");
  EXPECT_FALSE(iter.Valid());
  
  // Test seeking to key smaller than all
  iter.Seek("a");
  EXPECT_TRUE(iter.Valid());
  EXPECT_EQ("b", iter.key());
}

// Test backward iteration
TEST_F(SkipListTest, BackwardIteration) {
  std::vector<std::string> keys = {"a", "c", "e", "g"};
  for (const auto& key : keys) {
    list_->Insert(key);
  }
  
  StringSkipList::Iterator iter(list_);
  iter.SeekToLast();
  
  std::vector<std::string> result;
  while (iter.Valid()) {
    result.push_back(iter.key());
    iter.Prev();
  }
  
  std::vector<std::string> expected = {"g", "e", "c", "a"};
  EXPECT_EQ(expected, result);
}

// Test empty list
TEST_F(SkipListTest, EmptyList) {
  StringSkipList::Iterator iter(list_);
  
  iter.SeekToFirst();
  EXPECT_FALSE(iter.Valid());
  
  iter.SeekToLast();
  EXPECT_FALSE(iter.Valid());
  
  iter.Seek("any");
  EXPECT_FALSE(iter.Valid());
  
  EXPECT_FALSE(list_->Contains("any"));
}

// Test single element
TEST_F(SkipListTest, SingleElement) {
  list_->Insert("single");
  
  EXPECT_TRUE(list_->Contains("single"));
  EXPECT_FALSE(list_->Contains("other"));
  
  StringSkipList::Iterator iter(list_);
  
  iter.SeekToFirst();
  EXPECT_TRUE(iter.Valid());
  EXPECT_EQ("single", iter.key());
  iter.Next();
  EXPECT_FALSE(iter.Valid());
  
  iter.SeekToLast();
  EXPECT_TRUE(iter.Valid());
  EXPECT_EQ("single", iter.key());
  iter.Prev();
  EXPECT_FALSE(iter.Valid());
}

// Test large number of elements
TEST_F(SkipListTest, LargeDataSet) {
  const int num_elements = 1000;
  std::vector<std::string> keys;
  
  // Generate random keys
  std::random_device rd;
  std::mt19937 gen(rd());
  std::uniform_int_distribution<> dis(0, 999999);
  
  for (int i = 0; i < num_elements; ++i) {
    keys.push_back("key_" + std::to_string(dis(gen)));
  }
  
  // Remove duplicates and sort for comparison
  std::set<std::string> unique_keys(keys.begin(), keys.end());
  std::vector<std::string> sorted_keys(unique_keys.begin(), unique_keys.end());
  
  // Insert all unique keys (avoid duplicates)
  for (const auto& key : sorted_keys) {
    list_->Insert(key);
  }
  
  // Verify all unique keys are present
  for (const auto& key : sorted_keys) {
    EXPECT_TRUE(list_->Contains(key));
  }
  
  // Verify iteration order
  StringSkipList::Iterator iter(list_);
  iter.SeekToFirst();
  
  std::vector<std::string> result;
  while (iter.Valid()) {
    result.push_back(iter.key());
    iter.Next();
  }
  
  EXPECT_EQ(sorted_keys, result);
}

// Concurrent insertion test
TEST_F(SkipListTest, ConcurrentInsertion) {
  const int num_threads = 2;  // Reduce concurrency for stability
  const int insertions_per_thread = 50;  // Reduce operations per thread
  
  std::vector<std::thread> threads;
  std::vector<std::vector<std::string>> thread_keys(num_threads);
  
  // Prepare keys for each thread
  for (int t = 0; t < num_threads; ++t) {
    for (int i = 0; i < insertions_per_thread; ++i) {
      thread_keys[t].push_back("thread_" + std::to_string(t) + "_key_" + std::to_string(i));
    }
  }
  
  // Launch threads to insert concurrently
  for (int t = 0; t < num_threads; ++t) {
    threads.emplace_back([this, &thread_keys, t]() {
      for (const auto& key : thread_keys[t]) {
        list_->Insert(key);
        // Small delay to reduce race conditions
        std::this_thread::sleep_for(std::chrono::microseconds(1));
      }
    });
  }
  
  // Wait for all threads to complete
  for (auto& thread : threads) {
    thread.join();
  }
  
  // Verify all keys are present
  for (int t = 0; t < num_threads; ++t) {
    for (const auto& key : thread_keys[t]) {
      EXPECT_TRUE(list_->Contains(key));
    }
  }
  
  // Collect all expected keys and sort them
  std::vector<std::string> all_keys;
  for (const auto& keys : thread_keys) {
    all_keys.insert(all_keys.end(), keys.begin(), keys.end());
  }
  std::sort(all_keys.begin(), all_keys.end());
  
  // Verify iteration order is correct
  StringSkipList::Iterator iter(list_);
  iter.SeekToFirst();
  
  std::vector<std::string> result;
  while (iter.Valid()) {
    result.push_back(iter.key());
    iter.Next();
  }
  
  EXPECT_EQ(all_keys, result);
}

// Concurrent read-write test
TEST_F(SkipListTest, ConcurrentReadWrite) {
  const int num_writers = 2;
  const int num_readers = 2;
  const int operations_per_thread = 50;
  
  std::atomic<bool> stop_flag{false};
  std::vector<std::thread> threads;
  
  // Writer threads
  for (int w = 0; w < num_writers; ++w) {
    threads.emplace_back([this, w, operations_per_thread]() {
      for (int i = 0; i < operations_per_thread; ++i) {
        std::string key = "writer_" + std::to_string(w) + "_" + std::to_string(i);
        list_->Insert(key);
        // Small delay to allow interleaving
        std::this_thread::sleep_for(std::chrono::microseconds(1));
      }
    });
  }
  
  // Reader threads
  std::vector<int> read_counts(num_readers, 0);
  for (int r = 0; r < num_readers; ++r) {
    threads.emplace_back([this, &stop_flag, &read_counts, r]() {
      StringSkipList::Iterator iter(list_);
      while (!stop_flag.load()) {
        iter.SeekToFirst();
        while (iter.Valid()) {
          // Just iterate through, counting elements
          read_counts[r]++;
          iter.Next();
        }
        std::this_thread::sleep_for(std::chrono::microseconds(1));
      }
    });
  }
  
  // Wait for writers to complete
  for (int i = 0; i < num_writers; ++i) {
    threads[i].join();
  }
  
  // Stop readers
  stop_flag.store(true);
  for (int i = num_writers; i < num_writers + num_readers; ++i) {
    threads[i].join();
  }
  
  // Verify final state
  int expected_keys = num_writers * operations_per_thread;
  int actual_keys = 0;
  
  StringSkipList::Iterator iter(list_);
  iter.SeekToFirst();
  while (iter.Valid()) {
    actual_keys++;
    iter.Next();
  }
  
  EXPECT_EQ(expected_keys, actual_keys);
  
  // Verify readers were able to perform reads
  for (int r = 0; r < num_readers; ++r) {
    EXPECT_GT(read_counts[r], 0) << "Reader " << r << " didn't perform any reads";
  }
}

// Test with custom comparator (integer comparison)
TEST_F(SkipListTest, CustomComparator) {
  // Use integer skiplist with custom comparator
  struct IntComparator {
    int operator()(const std::string& a, const std::string& b) const {
      int int_a = std::stoi(a);
      int int_b = std::stoi(b);
      if (int_a < int_b) return -1;
      if (int_a > int_b) return 1;
      return 0;
    }
  };
  
  IntComparator int_cmp;
  SkipList<std::string, IntComparator> int_list(int_cmp);
  
  // Insert numbers as strings in random order
  std::vector<std::string> numbers = {"10", "5", "15", "1", "20", "3"};
  for (const auto& num : numbers) {
    int_list.Insert(num);
  }
  
  // Verify they are sorted numerically, not lexicographically
  SkipList<std::string, IntComparator>::Iterator iter(&int_list);
  iter.SeekToFirst();
  
  std::vector<std::string> result;
  while (iter.Valid()) {
    result.push_back(iter.key());
    iter.Next();
  }
  
  std::vector<std::string> expected = {"1", "3", "5", "10", "15", "20"};
  EXPECT_EQ(expected, result);
}
