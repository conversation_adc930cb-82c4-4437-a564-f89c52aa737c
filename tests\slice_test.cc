#include "kvstore/slice.h"
#include <gtest/gtest.h>
#include <string>

using namespace kvstore;

class SliceTest : public ::testing::Test {
 protected:
  void SetUp() override {}
  void TearDown() override {}
};

// Test basic construction
TEST_F(SliceTest, Construction) {
  // Default constructor
  Slice empty_slice;
  EXPECT_TRUE(empty_slice.empty());
  EXPECT_EQ(0, empty_slice.size());

  // From C string
  const char* cstr = "hello";
  Slice from_cstr(cstr);
  EXPECT_EQ(5, from_cstr.size());
  EXPECT_EQ(std::string("hello"), from_cstr.ToString());

  // From std::string
  std::string str = "world";
  Slice from_string(str);
  EXPECT_EQ(5, from_string.size());
  EXPECT_EQ(std::string("world"), from_string.ToString());

  // From char* and length
  const char* data = "test data";
  Slice from_data(data, 4);
  EXPECT_EQ(4, from_data.size());
  EXPECT_EQ(std::string("test"), from_data.ToString());
}

// Test copy semantics
TEST_F(SliceTest, CopySemantics) {
  Slice original("original");
  Slice copy = original;
  
  EXPECT_EQ(original.size(), copy.size());
  EXPECT_EQ(original.ToString(), copy.ToString());
  EXPECT_EQ(original.data(), copy.data());  // Should point to same memory
}

// Test comparison operations
TEST_F(SliceTest, Comparison) {
  Slice a("apple");
  Slice b("banana");
  Slice c("apple");

  // compare() method
  EXPECT_LT(a.compare(b), 0);  // "apple" < "banana"
  EXPECT_GT(b.compare(a), 0);  // "banana" > "apple"
  EXPECT_EQ(a.compare(c), 0);  // "apple" == "apple"

  // Equality operators
  EXPECT_TRUE(a == c);
  EXPECT_FALSE(a == b);
  EXPECT_TRUE(a != b);
  EXPECT_FALSE(a != c);
}

// Test starts_with method
TEST_F(SliceTest, StartsWith) {
  Slice text("hello world");
  Slice prefix1("hello");
  Slice prefix2("world");
  Slice prefix3("hello world extra");

  EXPECT_TRUE(text.starts_with(prefix1));
  EXPECT_FALSE(text.starts_with(prefix2));
  EXPECT_FALSE(text.starts_with(prefix3));

  // Empty prefix should always return true
  Slice empty_prefix;
  EXPECT_TRUE(text.starts_with(empty_prefix));
}

// Test array access operator
TEST_F(SliceTest, ArrayAccess) {
  Slice slice("test");
  
  EXPECT_EQ('t', slice[0]);
  EXPECT_EQ('e', slice[1]);
  EXPECT_EQ('s', slice[2]);
  EXPECT_EQ('t', slice[3]);
}

// Test remove_prefix method
TEST_F(SliceTest, RemovePrefix) {
  Slice slice("hello world");
  
  slice.remove_prefix(6);  // Remove "hello "
  EXPECT_EQ(std::string("world"), slice.ToString());
  EXPECT_EQ(5, slice.size());

  slice.remove_prefix(5);  // Remove remaining "world"
  EXPECT_TRUE(slice.empty());
  EXPECT_EQ(0, slice.size());
}

// Test clear method
TEST_F(SliceTest, Clear) {
  Slice slice("test data");
  EXPECT_FALSE(slice.empty());
  
  slice.clear();
  EXPECT_TRUE(slice.empty());
  EXPECT_EQ(0, slice.size());
  EXPECT_EQ(std::string(""), slice.ToString());
}

// Test with binary data (containing null bytes)
TEST_F(SliceTest, BinaryData) {
  const char binary_data[] = {'a', '\0', 'b', 'c', '\0'};
  Slice binary_slice(binary_data, 5);
  
  EXPECT_EQ(5, binary_slice.size());
  EXPECT_EQ('a', binary_slice[0]);
  EXPECT_EQ('\0', binary_slice[1]);
  EXPECT_EQ('b', binary_slice[2]);
  EXPECT_EQ('c', binary_slice[3]);
  EXPECT_EQ('\0', binary_slice[4]);
}

// Test edge cases
TEST_F(SliceTest, EdgeCases) {
  // Empty slice operations
  Slice empty;
  EXPECT_TRUE(empty.empty());
  EXPECT_EQ(0, empty.compare(empty));
  EXPECT_TRUE(empty == empty);
  
  // Single character slice
  Slice single("x");
  EXPECT_EQ(1, single.size());
  EXPECT_EQ('x', single[0]);
  EXPECT_EQ(std::string("x"), single.ToString());
  
  // Very long slice
  std::string long_string(10000, 'a');
  Slice long_slice(long_string);
  EXPECT_EQ(10000, long_slice.size());
  EXPECT_EQ(long_string, long_slice.ToString());
}

// Test lexicographic ordering
TEST_F(SliceTest, LexicographicOrdering) {
  std::vector<std::string> strings = {
    "",
    "a", 
    "aa",
    "ab",
    "b",
    "ba",
    "bb"
  };
  
  // Convert to slices
  std::vector<Slice> slices;
  for (const auto& s : strings) {
    slices.emplace_back(s);
  }
  
  // Verify ordering
  for (size_t i = 0; i < slices.size(); i++) {
    for (size_t j = i + 1; j < slices.size(); j++) {
      EXPECT_LT(slices[i].compare(slices[j]), 0) 
          << "Expected " << strings[i] << " < " << strings[j];
      EXPECT_GT(slices[j].compare(slices[i]), 0)
          << "Expected " << strings[j] << " > " << strings[i];
    }
  }
}
