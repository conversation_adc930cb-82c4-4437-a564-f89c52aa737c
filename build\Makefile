# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/hgfs/my_share/kvstore

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/hgfs/my_share/kvstore/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles /mnt/hgfs/my_share/kvstore/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named kvstore

# Build rule for target.
kvstore: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 kvstore
.PHONY : kvstore

# fast build rule for target.
kvstore/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/build
.PHONY : kvstore/fast

#=============================================================================
# Target rules for targets named slice_test

# Build rule for target.
slice_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 slice_test
.PHONY : slice_test

# fast build rule for target.
slice_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/build
.PHONY : slice_test/fast

#=============================================================================
# Target rules for targets named skiplist_test

# Build rule for target.
skiplist_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 skiplist_test
.PHONY : skiplist_test

# fast build rule for target.
skiplist_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/build
.PHONY : skiplist_test/fast

#=============================================================================
# Target rules for targets named memtable_test

# Build rule for target.
memtable_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 memtable_test
.PHONY : memtable_test

# fast build rule for target.
memtable_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/build
.PHONY : memtable_test/fast

#=============================================================================
# Target rules for targets named sstable_test

# Build rule for target.
sstable_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sstable_test
.PHONY : sstable_test

# fast build rule for target.
sstable_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/build
.PHONY : sstable_test/fast

#=============================================================================
# Target rules for targets named run_tests

# Build rule for target.
run_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 run_tests
.PHONY : run_tests

# fast build rule for target.
run_tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/build
.PHONY : run_tests/fast

src/comparator.o: src/comparator.cc.o
.PHONY : src/comparator.o

# target to build an object file
src/comparator.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/comparator.cc.o
.PHONY : src/comparator.cc.o

src/comparator.i: src/comparator.cc.i
.PHONY : src/comparator.i

# target to preprocess a source file
src/comparator.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/comparator.cc.i
.PHONY : src/comparator.cc.i

src/comparator.s: src/comparator.cc.s
.PHONY : src/comparator.s

# target to generate assembly for a file
src/comparator.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/comparator.cc.s
.PHONY : src/comparator.cc.s

src/db.o: src/db.cc.o
.PHONY : src/db.o

# target to build an object file
src/db.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/db.cc.o
.PHONY : src/db.cc.o

src/db.i: src/db.cc.i
.PHONY : src/db.i

# target to preprocess a source file
src/db.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/db.cc.i
.PHONY : src/db.cc.i

src/db.s: src/db.cc.s
.PHONY : src/db.s

# target to generate assembly for a file
src/db.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/db.cc.s
.PHONY : src/db.cc.s

src/memtable.o: src/memtable.cc.o
.PHONY : src/memtable.o

# target to build an object file
src/memtable.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/memtable.cc.o
.PHONY : src/memtable.cc.o

src/memtable.i: src/memtable.cc.i
.PHONY : src/memtable.i

# target to preprocess a source file
src/memtable.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/memtable.cc.i
.PHONY : src/memtable.cc.i

src/memtable.s: src/memtable.cc.s
.PHONY : src/memtable.s

# target to generate assembly for a file
src/memtable.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/memtable.cc.s
.PHONY : src/memtable.cc.s

src/slice.o: src/slice.cc.o
.PHONY : src/slice.o

# target to build an object file
src/slice.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/slice.cc.o
.PHONY : src/slice.cc.o

src/slice.i: src/slice.cc.i
.PHONY : src/slice.i

# target to preprocess a source file
src/slice.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/slice.cc.i
.PHONY : src/slice.cc.i

src/slice.s: src/slice.cc.s
.PHONY : src/slice.s

# target to generate assembly for a file
src/slice.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/slice.cc.s
.PHONY : src/slice.cc.s

src/sstable.o: src/sstable.cc.o
.PHONY : src/sstable.o

# target to build an object file
src/sstable.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/sstable.cc.o
.PHONY : src/sstable.cc.o

src/sstable.i: src/sstable.cc.i
.PHONY : src/sstable.i

# target to preprocess a source file
src/sstable.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/sstable.cc.i
.PHONY : src/sstable.cc.i

src/sstable.s: src/sstable.cc.s
.PHONY : src/sstable.s

# target to generate assembly for a file
src/sstable.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/sstable.cc.s
.PHONY : src/sstable.cc.s

src/sstable_builder.o: src/sstable_builder.cc.o
.PHONY : src/sstable_builder.o

# target to build an object file
src/sstable_builder.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/sstable_builder.cc.o
.PHONY : src/sstable_builder.cc.o

src/sstable_builder.i: src/sstable_builder.cc.i
.PHONY : src/sstable_builder.i

# target to preprocess a source file
src/sstable_builder.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/sstable_builder.cc.i
.PHONY : src/sstable_builder.cc.i

src/sstable_builder.s: src/sstable_builder.cc.s
.PHONY : src/sstable_builder.s

# target to generate assembly for a file
src/sstable_builder.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/sstable_builder.cc.s
.PHONY : src/sstable_builder.cc.s

src/status.o: src/status.cc.o
.PHONY : src/status.o

# target to build an object file
src/status.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/status.cc.o
.PHONY : src/status.cc.o

src/status.i: src/status.cc.i
.PHONY : src/status.i

# target to preprocess a source file
src/status.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/status.cc.i
.PHONY : src/status.cc.i

src/status.s: src/status.cc.s
.PHONY : src/status.s

# target to generate assembly for a file
src/status.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/src/status.cc.s
.PHONY : src/status.cc.s

tests/memtable_test.o: tests/memtable_test.cc.o
.PHONY : tests/memtable_test.o

# target to build an object file
tests/memtable_test.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/tests/memtable_test.cc.o
.PHONY : tests/memtable_test.cc.o

tests/memtable_test.i: tests/memtable_test.cc.i
.PHONY : tests/memtable_test.i

# target to preprocess a source file
tests/memtable_test.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/tests/memtable_test.cc.i
.PHONY : tests/memtable_test.cc.i

tests/memtable_test.s: tests/memtable_test.cc.s
.PHONY : tests/memtable_test.s

# target to generate assembly for a file
tests/memtable_test.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/tests/memtable_test.cc.s
.PHONY : tests/memtable_test.cc.s

tests/skiplist_test.o: tests/skiplist_test.cc.o
.PHONY : tests/skiplist_test.o

# target to build an object file
tests/skiplist_test.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/tests/skiplist_test.cc.o
.PHONY : tests/skiplist_test.cc.o

tests/skiplist_test.i: tests/skiplist_test.cc.i
.PHONY : tests/skiplist_test.i

# target to preprocess a source file
tests/skiplist_test.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/tests/skiplist_test.cc.i
.PHONY : tests/skiplist_test.cc.i

tests/skiplist_test.s: tests/skiplist_test.cc.s
.PHONY : tests/skiplist_test.s

# target to generate assembly for a file
tests/skiplist_test.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/tests/skiplist_test.cc.s
.PHONY : tests/skiplist_test.cc.s

tests/slice_test.o: tests/slice_test.cc.o
.PHONY : tests/slice_test.o

# target to build an object file
tests/slice_test.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/tests/slice_test.cc.o
.PHONY : tests/slice_test.cc.o

tests/slice_test.i: tests/slice_test.cc.i
.PHONY : tests/slice_test.i

# target to preprocess a source file
tests/slice_test.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/tests/slice_test.cc.i
.PHONY : tests/slice_test.cc.i

tests/slice_test.s: tests/slice_test.cc.s
.PHONY : tests/slice_test.s

# target to generate assembly for a file
tests/slice_test.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/tests/slice_test.cc.s
.PHONY : tests/slice_test.cc.s

tests/sstable_test.o: tests/sstable_test.cc.o
.PHONY : tests/sstable_test.o

# target to build an object file
tests/sstable_test.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/tests/sstable_test.cc.o
.PHONY : tests/sstable_test.cc.o

tests/sstable_test.i: tests/sstable_test.cc.i
.PHONY : tests/sstable_test.i

# target to preprocess a source file
tests/sstable_test.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/tests/sstable_test.cc.i
.PHONY : tests/sstable_test.cc.i

tests/sstable_test.s: tests/sstable_test.cc.s
.PHONY : tests/sstable_test.s

# target to generate assembly for a file
tests/sstable_test.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/tests/sstable_test.cc.s
.PHONY : tests/sstable_test.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... run_tests"
	@echo "... kvstore"
	@echo "... memtable_test"
	@echo "... skiplist_test"
	@echo "... slice_test"
	@echo "... sstable_test"
	@echo "... src/comparator.o"
	@echo "... src/comparator.i"
	@echo "... src/comparator.s"
	@echo "... src/db.o"
	@echo "... src/db.i"
	@echo "... src/db.s"
	@echo "... src/memtable.o"
	@echo "... src/memtable.i"
	@echo "... src/memtable.s"
	@echo "... src/slice.o"
	@echo "... src/slice.i"
	@echo "... src/slice.s"
	@echo "... src/sstable.o"
	@echo "... src/sstable.i"
	@echo "... src/sstable.s"
	@echo "... src/sstable_builder.o"
	@echo "... src/sstable_builder.i"
	@echo "... src/sstable_builder.s"
	@echo "... src/status.o"
	@echo "... src/status.i"
	@echo "... src/status.s"
	@echo "... tests/memtable_test.o"
	@echo "... tests/memtable_test.i"
	@echo "... tests/memtable_test.s"
	@echo "... tests/skiplist_test.o"
	@echo "... tests/skiplist_test.i"
	@echo "... tests/skiplist_test.s"
	@echo "... tests/slice_test.o"
	@echo "... tests/slice_test.i"
	@echo "... tests/slice_test.s"
	@echo "... tests/sstable_test.o"
	@echo "... tests/sstable_test.i"
	@echo "... tests/sstable_test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

