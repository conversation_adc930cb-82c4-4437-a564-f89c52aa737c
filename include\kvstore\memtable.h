#ifndef KVSTORE_MEMTABLE_H_
#define KVSTORE_MEMTABLE_H_

#include "kvstore/skiplist.h"
#include "kvstore/slice.h"
#include "kvstore/status.h"

namespace kvstore {

class Comparator;

class MemTable {
 public:
  // MemTables are reference counted.  The initial reference count
  // is zero and the caller must call Ref() at least once.
  explicit MemTable(const Comparator* comparator);

  MemTable(const MemTable&) = delete;
  MemTable& operator=(const MemTable&) = delete;

  // Increase reference count.
  void Ref() { ++refs_; }

  // Drop reference count.  Delete if no more references exist.
  void Unref() {
    --refs_;
    assert(refs_ >= 0);
    if (refs_ <= 0) {
      delete this;
    }
  }

  // Returns an estimate of the number of bytes of data in use by this
  // data structure. It is safe to call when MemTable is being modified.
  size_t ApproximateMemoryUsage();

  // Return an iterator that yields the contents of the memtable.
  //
  // The caller must ensure that the underlying MemTable remains live
  // while the returned iterator is live.  The keys returned by this
  // iterator are internal keys encoded by AppendInternalKey in the
  // db/format.{h,cc} module.
  class Iterator {
   public:
    // Initialize an iterator over the specified memtable.
    explicit Iterator(MemTable* mem);
    ~Iterator();

    Iterator(const Iterator&) = delete;
    Iterator& operator=(const Iterator&) = delete;

    // Returns true iff the iterator is positioned at a valid entry.
    bool Valid() const;

    // Position at the first entry in memtable.
    // Final state of iterator is Valid() iff memtable is not empty.
    void SeekToFirst();

    // Position at the last entry in memtable.
    // Final state of iterator is Valid() iff memtable is not empty.
    void SeekToLast();

    // Advance to the next entry.
    // REQUIRES: Valid()
    void Next();

    // Advance to the previous entry.
    // REQUIRES: Valid()
    void Prev();

    // Advance to the first entry with key >= target
    void Seek(const Slice& target);

    // Returns the key at the current position.
    // REQUIRES: Valid()
    Slice key() const;

    // Returns the value at the current position.
    // REQUIRES: Valid()
    Slice value() const;

   private:
    void* iter_;  // Actual iterator type is hidden
  };

  // Return an iterator over the contents of the memtable.
  Iterator* NewIterator();

  // Add an entry into memtable that maps key to value.
  // REQUIRES: nothing that compares equal to key is currently in the memtable.
  void Add(const Slice& key, const Slice& value);

  // If memtable contains a value for key, store it in *value and return true.
  // If memtable contains a deletion for key, store a NotFound() error
  // in *status and return true.
  // Else, return false.
  bool Get(const Slice& key, std::string* value, Status* s);

 private:
  friend class MemTableIterator;
  struct KeyComparator {
    const Comparator* comparator;
    explicit KeyComparator(const Comparator* c) : comparator(c) {}
    int operator()(const char* a, const char* b) const;
  };

  ~MemTable();  // Private since only Unref() should be used to delete it

  KeyComparator comparator_;
  int refs_;
  SkipList<const char*, KeyComparator> table_;
};

}  // namespace kvstore

#endif  // KVSTORE_MEMTABLE_H_
