cmake_minimum_required(VERSION 3.16)
project(MyKVStore VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4)
    add_compile_options(/wd4996)  # Disable deprecation warnings for MSVC
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Debug/Release configurations
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Source files
set(KVSTORE_SOURCES
    src/slice.cc
    src/status.cc
    src/comparator.cc
    src/memtable.cc
    src/sstable_builder.cc
    src/sstable.cc
    src/db.cc
)

# Create static library
add_library(kvstore STATIC ${KVSTORE_SOURCES})

# Include directories
target_include_directories(kvstore PUBLIC 
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Platform-specific libraries
if(WIN32)
    target_link_libraries(kvstore PRIVATE)
else()
    find_package(Threads REQUIRED)
    target_link_libraries(kvstore PRIVATE Threads::Threads)
endif()

# Enable testing
enable_testing()

# Try to find GoogleTest
find_package(GTest QUIET)

if(GTest_FOUND)
    message(STATUS "Found GoogleTest")
    
    # Test sources
    set(TEST_SOURCES
        tests/slice_test.cc
        tests/skiplist_test.cc
        tests/memtable_test.cc
        tests/sstable_test.cc
    )

    # Create test executables
    foreach(test_src ${TEST_SOURCES})
        get_filename_component(test_name ${test_src} NAME_WE)
        add_executable(${test_name} ${test_src})
        target_link_libraries(${test_name} PRIVATE kvstore GTest::gtest GTest::gtest_main)
        add_test(NAME ${test_name} COMMAND ${test_name})
    endforeach()
    
    # Add custom target to run all tests
    add_custom_target(run_tests
        COMMAND ${CMAKE_CTEST_COMMAND} --verbose
        DEPENDS slice_test skiplist_test memtable_test sstable_test
        COMMENT "Running all tests"
    )
else()
    message(WARNING "GoogleTest not found. Tests will not be built.")
    message(STATUS "To install GoogleTest on Windows with vcpkg:")
    message(STATUS "  vcpkg install gtest:x64-windows")
    message(STATUS "  cmake -DCMAKE_TOOLCHAIN_FILE=[path to vcpkg]/scripts/buildsystems/vcpkg.cmake ..")
endif()

# Installation
include(GNUInstallDirs)

install(TARGETS kvstore
    EXPORT MyKVStoreTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

install(DIRECTORY include/kvstore
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

# Export targets
install(EXPORT MyKVStoreTargets
    FILE MyKVStoreTargets.cmake
    NAMESPACE MyKVStore::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/MyKVStore
)

# Create config file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    MyKVStoreConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

install(FILES MyKVStoreConfigVersion.cmake
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/MyKVStore
)
