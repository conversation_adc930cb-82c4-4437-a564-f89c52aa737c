# CMake generated Testfile for 
# Source directory: /mnt/hgfs/my_share/kvstore
# Build directory: /mnt/hgfs/my_share/kvstore/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(slice_test "/mnt/hgfs/my_share/kvstore/build/slice_test")
set_tests_properties(slice_test PROPERTIES  _BACKTRACE_TRIPLES "/mnt/hgfs/my_share/kvstore/CMakeLists.txt;72;add_test;/mnt/hgfs/my_share/kvstore/CMakeLists.txt;0;")
add_test(skiplist_test "/mnt/hgfs/my_share/kvstore/build/skiplist_test")
set_tests_properties(skiplist_test PROPERTIES  _BACKTRACE_TRIPLES "/mnt/hgfs/my_share/kvstore/CMakeLists.txt;72;add_test;/mnt/hgfs/my_share/kvstore/CMakeLists.txt;0;")
add_test(memtable_test "/mnt/hgfs/my_share/kvstore/build/memtable_test")
set_tests_properties(memtable_test PROPERTIES  _BACKTRACE_TRIPLES "/mnt/hgfs/my_share/kvstore/CMakeLists.txt;72;add_test;/mnt/hgfs/my_share/kvstore/CMakeLists.txt;0;")
add_test(sstable_test "/mnt/hgfs/my_share/kvstore/build/sstable_test")
set_tests_properties(sstable_test PROPERTIES  _BACKTRACE_TRIPLES "/mnt/hgfs/my_share/kvstore/CMakeLists.txt;72;add_test;/mnt/hgfs/my_share/kvstore/CMakeLists.txt;0;")
