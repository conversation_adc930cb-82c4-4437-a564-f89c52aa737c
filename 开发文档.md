好的，没问题！

这是一份为您量身定制的、工作量适中的 **“基于 LSM-Tree 的高性能单机 KV 存储引擎”** 开发文档。

这份文档将项目分解为六个核心阶段，每个阶段都有明确的目标、要实现的关键组件和测试要点。您可以按照这个路线图逐步推进，每完成一个阶段，都会得到一个可运行、可验证的中间成果。这非常有助于保持开发节奏和成就感。

---

### **项目开发文档：C++ 单机 KV 存储引擎 (MyKVStore)**

#### **1. 项目概述**

*   **项目名称**：MyKVStore
*   **目标**：从零开始构建一个基于 LSM-Tree 架构的、持久化的、支持崩溃恢复的高性能单机键值存储引擎。
*   **核心架构**：LSM-Tree (Log-Structured Merge-Tree)
*   **技术栈**：
    *   **语言**：C++17
    *   **构建工具**：CMake
    *   **测试框架**：GoogleTest (强烈推荐)

#### **2. 核心组件设计**

在开始编码前，请先理解这几个核心概念：

*   **MemTable**: 内存中的数据结构，用于接收最新的写入。我们选用**跳表 (SkipList)**，因为它是有序的，且范围查询和并发性能优秀。
*   **Immutable MemTable**: 当 MemTable 写满后，会转变为一个只读的 Immutable MemTable，等待被刷写到磁盘。
*   **WAL (Write-Ahead Log)**: 预写日志。所有写操作必须先以顺序追加的方式写入该日志文件，成功后再写入 MemTable。这是实现数据持久性和崩溃恢复的基石。
*   **SSTable (Sorted String Table)**: 磁盘上的数据文件。Immutable MemTable 中的数据会经过排序后，持久化为 SSTable 文件。SSTable 是只读的。
*   **Compaction**: 后台任务，用于合并多个 SSTable 文件，以清除冗余数据（被覆盖或删除的键），并优化读取性能。

---

### **3. 分阶段开发路线图**

#### **阶段一：基础架构与内存核心 (预计 1 周)**

*   **目标**：搭建项目骨架，并实现一个纯内存的、基于跳表的 KV 存储。
*   **关键组件实现**：
    1.  **项目结构**：
        *   创建 `CMakeLists.txt`。
        *   建立目录结构：`src/`, `include/`, `tests/`。
    2.  **跳表 (SkipList)**：
        *   在 `include/skiplist.h` 和 `src/skiplist.cc` 中实现一个线程安全的跳表。
        *   接口应包含：`Insert(key, value)`，`Search(key, &value)`，`Delete(key)`。
        *   这是项目的第一个核心数据结构，值得花时间打磨。
    3.  **MemTable 类**：
        *   在 `memtable.h` 中创建 `MemTable` 类。
        *   内部封装一个 `SkipList` 实例。
        *   对外提供 `Put(key, value)` 和 `Get(key, &value)` 接口。
*   **测试要点**：
    *   使用 GoogleTest 对 `SkipList` 进行充分的单元测试（插入、查找、删除、并发插入）。
    *   测试 `MemTable` 的 `Put` 和 `Get` 功能是否正确。

#### **阶段二：持久化 - 将内存刷写到磁盘 (预计 1-2 周)**

*   **目标**：实现将 MemTable 内容持久化为 SSTable 文件的能力。
*   **关键组件实现**：
    1.  **SSTable 文件格式定义**：
        *   设计一个简单的 SSTable 格式。例如，文件由多个 `[Key长度][Key][Value长度][Value]` 的记录连续组成。
        *   **进阶版**：可以设计成 `[Data Block 1][Data Block 2]...[Index Block][Footer]` 的格式，更接近工业级实现。初期可以先用简单版。
    2.  **SSTable Builder 类**：
        *   创建 `sstable_builder.h`。
        *   功能：接收一系列有序的 KV 对，并按照你定义的格式生成一个 SSTable 文件。
        *   提供 `Add(key, value)` 和 `Finish()` 接口。
    3.  **DB 主流程修改**：
        *   在主类中，实现一个 `FlushMemTable()` 函数。
        *   当 MemTable 达到一定大小时（例如 4MB），将其转换为 Immutable MemTable。
        *   创建一个后台线程（或同步执行），遍历 Immutable MemTable，使用 `SSTable Builder` 将其内容写入一个新的 SSTable 文件（文件名可以用递增的数字）。
*   **测试要点**：
    *   编写一个测试，不断 `Put` 数据直到触发 flush。
    *   验证生成的 SSTable 文件内容是否正确。
    *   修改 `Get` 逻辑：先查 MemTable，如果找不到，则需要**依次**查找所有已生成的 SSTable 文件。

#### **阶段三：崩溃恢复 - 实现 WAL (预计 1 周)**

*   **目标**：引入 WAL 机制，确保在程序崩溃时，内存中尚未刷盘的数据不会丢失。
*   **关键组件实现**：
    1.  **LogWriter 类**：
        *   实现一个日志写入类，提供 `AddRecord(record)` 方法，以纯追加模式向日志文件写入。
        *   注意：每次写入后需要 `fsync` 确保落盘（为了简化，可以先这么做）。
    2.  **修改 Put 流程**：
        *   **关键**：现在的 `Put(key, value)` 流程必须是：
            1.  将操作（Key 和 Value）写入 WAL 文件并刷盘。
            2.  成功后，再将数据写入 MemTable。
    3.  **启动时恢复 (Recovery)**：
        *   在数据库启动时，检查是否存在 WAL 文件。
        *   如果存在，读取 WAL 文件中的所有记录，并依次将它们重新插入到**新创建的 MemTable** 中。这样就恢复了崩溃前的内存状态。
*   **测试要点**：
    *   编写一个程序，`Put` 几条数据后，不正常退出（例如 `kill -9`）。
    *   重启程序，不进行任何写操作，直接 `Get` 之前写入的数据，验证是否能成功取回。

#### **阶段四：核心机制 - Compaction (预计 1.5 周)**

*   **目标**：实现后台 Compaction，合并 SSTable，回收空间，提升读取效率。这是 LSM-Tree 的精髓。
*   **简化版 Compaction 实现**：
    1.  **分层 (Leveling)**：引入 Level-0 和 Level-1 的概念。
        *   所有新刷盘的 SSTable 文件都进入 Level-0。Level-0 的文件之间可能有 Key 的重叠。
        *   Level-1 的文件内部的 Key 是有序的，且文件之间 Key 不重叠。
    2.  **Compaction 触发**：当 Level-0 的文件数量超过一个阈值（例如 4 个）时，触发 Compaction。
    3.  **Compaction 过程**：
        *   选择 Level-0 的所有文件，与它们在 Level-1 中有键范围重叠的文件进行合并。
        *   使用类似**多路归并排序**的算法，读取所有参与合并的 SSTable 的内容，逐一比较，将最新的版本写入**新**的 Level-1 SSTable 文件中。
        *   合并完成后，删除所有旧的、被合并的 SSTable 文件。
*   **测试要点**：
    *   手动创建几个有重叠 Key 的 Level-0 SSTable 文件。
    *   运行 Compaction 过程，验证生成的新 Level-1 文件内容是否正确（没有重复 Key，删除了旧值）。
    *   验证旧文件是否被删除。
    *   修改 `Get` 逻辑以适应分层结构：MemTable -> Immutable MemTable -> Level-0 -> Level-1。

#### **阶段五：性能优化 (预计 1 周)**

*   **目标**：为你的 KV 引擎加入工业级的优化手段，这是面试中的巨大亮点。
*   **关键组件实现**：
    1.  **布隆过滤器 (Bloom Filter)**：
        *   在 `SSTable Builder` 生成 SSTable 时，同时为文件中的所有 Key 生成一个布隆过滤器，并存入文件末尾的 `Footer` 部分。
        *   当查询一个 Key 时，先通过内存中的布隆过滤器快速判断这个 Key **是否可能存在**于对应的 SSTable 文件中。如果不存在，则直接跳过对该文件的 I/O 操作。
    2.  **缓存 (Block Cache)**：
        *   实现一个 LRU 缓存。
        *   当从 SSTable 读取数据块（Data Block）时，将其放入缓存。
        *   下次再需要读取相同的数据块时，直接从缓存中获取，避免磁盘 I/O。
*   **测试要点**：
    *   进行基准性能测试，对比开启/关闭布隆过滤器和缓存时的 `Get` 操作性能差异。

#### **阶段六：API 封装与并发控制 (预计 0.5 周)**

*   **目标**：提供简洁易用的 API，并保证多线程安全。
*   **关键组件实现**：
    1.  **DB 接口类**：
        *   创建一个顶层的 `DB` 类或 `KVStore` 类。
        *   将所有内部组件（MemTable, WAL, Compaction 线程等）封装起来。
        *   对外只暴露 `Open(path)`, `Put(key, value)`, `Get(key, &value)`, `Delete(key)`, `Close()` 等接口。
    2.  **线程安全**：
        *   在 `Put`, `Get`, `Delete` 等公共接口的入口处，加入**读写锁 (Read-Write Lock)** 或互斥锁 (Mutex)，保护对内部状态的并发访问。
*   **测试要点**：
    *   编写多线程测试用例，并发地进行读写操作，验证数据的正确性和程序的稳定性。

---

### **面试准备建议**

在完成项目后，请准备好回答以下问题：
1.  **为什么选择 LSM-Tree 架构？它有什么优缺点？** (写友好，顺序 I/O；读放大，Compaction 开销)。
2.  **描述一下你的系统一次写操作 (Put) 的完整流程。** (WAL -> MemTable -> ...)。
3.  **描述一下一次读操作 (Get) 的查找路径和最坏情况。** (MemTable -> ... -> 所有 SSTable)。
4.  **系统是如何保证崩溃后数据不丢失的？** (WAL 机制)。
5.  **Compaction 的作用是什么？你是如何实现的？** (回收空间，优化读取)。
6.  **你做了哪些性能优化？效果如何？** (布隆过滤器，Block Cache)。
7.  **项目最大的挑战是什么？你是如何解决的？** (例如：Compaction 逻辑的复杂性，多线程 bug 的调试)。

祝你项目顺利，秋招成功！