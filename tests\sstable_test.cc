#include "kvstore/sstable_builder.h"
#include "kvstore/sstable.h"
#include "kvstore/db.h"
#include "kvstore/comparator.h"
#include <gtest/gtest.h>
#include <filesystem>
#include <string>
#include <cstdio>

namespace kvstore {

class SSTableTest : public ::testing::Test {
 protected:
  void SetUp() override {
    test_dir_ = "./test_sstable_data";
    
    // 清理测试目录
    std::error_code ec;
    std::filesystem::remove_all(test_dir_, ec);
    std::filesystem::create_directories(test_dir_, ec);
    
    comparator_ = BytewiseComparator();
  }

  void TearDown() override {
    // 清理测试目录
    std::error_code ec;
    std::filesystem::remove_all(test_dir_, ec);
  }

  std::string test_dir_;
  const Comparator* comparator_;
  
  std::string TestFileName(const std::string& name) {
    return test_dir_ + "/" + name + ".sst";
  }
};

// 测试SSTableBuilder基础功能
TEST_F(SSTableTest, BuilderBasicOperations) {
  std::string filename = TestFileName("basic");
  
  // 创建并写入数据
  {
    SSTableBuilder builder(filename);
    
    ASSERT_TRUE(builder.Add("key1", "value1").ok());
    ASSERT_TRUE(builder.Add("key2", "value2").ok());
    ASSERT_TRUE(builder.Add("key3", "value3").ok());
    
    EXPECT_EQ(3u, builder.NumEntries());
    EXPECT_GT(builder.FileSize(), 0u);
    
    uint64_t file_size;
    ASSERT_TRUE(builder.Finish(&file_size).ok());
    EXPECT_GT(file_size, 0u);
  }
  
  // 验证文件存在
  EXPECT_TRUE(std::filesystem::exists(filename));
}

// 测试SSTableBuilder键顺序验证
TEST_F(SSTableTest, BuilderKeyOrdering) {
  std::string filename = TestFileName("ordering");
  SSTableBuilder builder(filename);
  
  ASSERT_TRUE(builder.Add("key1", "value1").ok());
  ASSERT_TRUE(builder.Add("key2", "value2").ok());
  
  // 尝试添加乱序的键，应该失败
  Status s = builder.Add("key1", "value1_duplicate");
  EXPECT_FALSE(s.ok());
  EXPECT_TRUE(s.ToString().find("字典序") != std::string::npos);
  
  builder.Abandon();
}

// 测试SSTable读取功能
TEST_F(SSTableTest, SSTableRead) {
  std::string filename = TestFileName("read");
  
  // 先写入数据
  {
    SSTableBuilder builder(filename);
    ASSERT_TRUE(builder.Add("apple", "红苹果").ok());
    ASSERT_TRUE(builder.Add("banana", "黄香蕉").ok());
    ASSERT_TRUE(builder.Add("cherry", "红樱桃").ok());
    ASSERT_TRUE(builder.Finish().ok());
  }
  
  // 打开并读取数据
  std::unique_ptr<SSTable> sstable;
  ASSERT_TRUE(SSTable::Open(filename, comparator_, &sstable).ok());
  
  // 测试统计信息
  EXPECT_EQ(3u, sstable->NumEntries());
  EXPECT_GT(sstable->FileSize(), 0u);
  EXPECT_EQ("apple", sstable->FirstKey().ToString());
  EXPECT_EQ("cherry", sstable->LastKey().ToString());
  
  // 测试Get操作
  std::string value;
  ASSERT_TRUE(sstable->Get("banana", &value).ok());
  EXPECT_EQ("黄香蕉", value);
  
  ASSERT_TRUE(sstable->Get("apple", &value).ok());
  EXPECT_EQ("红苹果", value);
  
  ASSERT_TRUE(sstable->Get("cherry", &value).ok());
  EXPECT_EQ("红樱桃", value);
  
  // 测试不存在的键
  Status s = sstable->Get("grape", &value);
  EXPECT_TRUE(s.IsNotFound());
}

// 测试SSTableIterator遍历功能
TEST_F(SSTableTest, IteratorTraversal) {
  std::string filename = TestFileName("iterator");
  
  // 写入测试数据
  {
    SSTableBuilder builder(filename);
    ASSERT_TRUE(builder.Add("key01", "value01").ok());
    ASSERT_TRUE(builder.Add("key02", "value02").ok());
    ASSERT_TRUE(builder.Add("key03", "value03").ok());
    ASSERT_TRUE(builder.Add("key04", "value04").ok());
    ASSERT_TRUE(builder.Add("key05", "value05").ok());
    ASSERT_TRUE(builder.Finish().ok());
  }
  
  // 打开SSTable并测试遍历
  std::unique_ptr<SSTable> sstable;
  ASSERT_TRUE(SSTable::Open(filename, comparator_, &sstable).ok());
  
  auto iter = sstable->NewIterator();
  
  // 测试正向遍历
  std::vector<std::pair<std::string, std::string>> entries;
  for (iter->SeekToFirst(); iter->Valid(); iter->Next()) {
    entries.emplace_back(iter->key().ToString(), iter->value().ToString());
  }
  
  ASSERT_EQ(5u, entries.size());
  EXPECT_EQ("key01", entries[0].first);
  EXPECT_EQ("value01", entries[0].second);
  EXPECT_EQ("key05", entries[4].first);
  EXPECT_EQ("value05", entries[4].second);
  
  // 测试SeekToLast
  iter->SeekToLast();
  ASSERT_TRUE(iter->Valid());
  EXPECT_EQ("key05", iter->key().ToString());
  EXPECT_EQ("value05", iter->value().ToString());
  
  // 测试Seek功能
  iter->Seek("key03");
  ASSERT_TRUE(iter->Valid());
  EXPECT_EQ("key03", iter->key().ToString());
  
  iter->Seek("key025");  // 介于key02和key03之间
  ASSERT_TRUE(iter->Valid());
  EXPECT_EQ("key03", iter->key().ToString());  // 应该定位到key03
  
  iter->Seek("key99");  // 超出范围
  EXPECT_FALSE(iter->Valid());
}

// 测试空SSTable
TEST_F(SSTableTest, EmptySSTable) {
  std::string filename = TestFileName("empty");
  
  // 创建空的SSTable
  {
    SSTableBuilder builder(filename);
    ASSERT_TRUE(builder.Finish().ok());
  }
  
  // 打开并验证
  std::unique_ptr<SSTable> sstable;
  ASSERT_TRUE(SSTable::Open(filename, comparator_, &sstable).ok());
  
  EXPECT_EQ(0u, sstable->NumEntries());
  
  // 测试Get操作
  std::string value;
  Status s = sstable->Get("any_key", &value);
  EXPECT_TRUE(s.IsNotFound());
  
  // 测试Iterator
  auto iter = sstable->NewIterator();
  iter->SeekToFirst();
  EXPECT_FALSE(iter->Valid());
}

// 测试大数据SSTable
TEST_F(SSTableTest, LargeDataSSTable) {
  std::string filename = TestFileName("large");
  
  const int num_entries = 1000;
  
  // 写入大量数据
  {
    SSTableBuilder builder(filename);
    
    for (int i = 0; i < num_entries; ++i) {
      // 使用固定宽度的数字确保字典序正确
      char key_buffer[32];
      snprintf(key_buffer, sizeof(key_buffer), "key_%06d", i * 100);
      std::string key = key_buffer;
      std::string value = "这是一个比较长的值_" + std::to_string(i) + "_用于测试大数据处理能力";
      ASSERT_TRUE(builder.Add(key, value).ok());
    }
    
    EXPECT_EQ(static_cast<uint64_t>(num_entries), builder.NumEntries());
    ASSERT_TRUE(builder.Finish().ok());
  }
  
  // 读取并验证
  std::unique_ptr<SSTable> sstable;
  ASSERT_TRUE(SSTable::Open(filename, comparator_, &sstable).ok());
  
  EXPECT_EQ(static_cast<uint64_t>(num_entries), sstable->NumEntries());
  
  // 随机验证一些条目
  for (int i = 0; i < num_entries; i += 100) {
    char key_buffer[32];
    snprintf(key_buffer, sizeof(key_buffer), "key_%06d", i * 100);
    std::string key = key_buffer;
    std::string expected_value = "这是一个比较长的值_" + std::to_string(i) + "_用于测试大数据处理能力";
    
    std::string actual_value;
    ASSERT_TRUE(sstable->Get(key, &actual_value).ok());
    EXPECT_EQ(expected_value, actual_value);
  }
}

// 测试DB端到端刷盘功能
TEST_F(SSTableTest, DatabaseFlushIntegration) {
  Options options;
  options.db_path = test_dir_ + "/db";
  options.memtable_flush_threshold = 1024;  // 设置较小的阈值便于测试
  
  std::unique_ptr<DB> db;
  ASSERT_TRUE(DB::Open(options, &db).ok());
  
  // 写入足够多的数据触发刷盘
  for (int i = 0; i < 100; ++i) {
    std::string key = "test_key_" + std::to_string(i);
    std::string value = "这是测试值_" + std::to_string(i) + "_用于验证刷盘功能是否正常工作";
    ASSERT_TRUE(db->Put(key, value).ok());
  }
  
  // 手动触发刷盘
  ASSERT_TRUE(db->FlushMemTable().ok());
  
  // 验证数据仍然可以读取
  for (int i = 0; i < 100; ++i) {
    std::string key = "test_key_" + std::to_string(i);
    std::string expected_value = "这是测试值_" + std::to_string(i) + "_用于验证刷盘功能是否正常工作";
    
    std::string actual_value;
    ASSERT_TRUE(db->Get(key, &actual_value).ok());
    EXPECT_EQ(expected_value, actual_value);
  }
  
  // 检查统计信息
  DB::Stats stats;
  db->GetStats(&stats);
  EXPECT_GT(stats.num_sstables, 0u);
  EXPECT_GT(stats.total_sstable_size, 0u);
  EXPECT_GE(stats.num_entries, 100u);
  
  ASSERT_TRUE(db->Close().ok());
}

// 测试文件名解析功能
TEST_F(SSTableTest, FileNameParsing) {
  // 测试有效文件名
  uint64_t seq_number;
  EXPECT_TRUE(ParseSSTableFileName("000001.sst", &seq_number));
  EXPECT_EQ(1u, seq_number);
  
  EXPECT_TRUE(ParseSSTableFileName("999999.sst", &seq_number));
  EXPECT_EQ(999999u, seq_number);
  
  // 测试无效文件名
  EXPECT_FALSE(ParseSSTableFileName("001.sst", &seq_number));      // 太短
  EXPECT_FALSE(ParseSSTableFileName("0000001.sst", &seq_number));  // 太长
  EXPECT_FALSE(ParseSSTableFileName("000001.txt", &seq_number));   // 错误扩展名
  EXPECT_FALSE(ParseSSTableFileName("abcdef.sst", &seq_number));   // 非数字
  
  // 测试文件名生成
  EXPECT_EQ("000001.sst", SSTableFileName(1));
  EXPECT_EQ("000042.sst", SSTableFileName(42));
  EXPECT_EQ("999999.sst", SSTableFileName(999999));
}

}  // namespace kvstore
