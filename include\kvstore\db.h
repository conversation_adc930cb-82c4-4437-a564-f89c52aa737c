#ifndef KVSTORE_DB_H_
#define KVSTORE_DB_H_

#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include "kvstore/slice.h"
#include "kvstore/status.h"

namespace kvstore {

class MemTable;
class SSTable;
class Comparator;

// 数据库配置选项
struct Options {
  // 用于key比较的比较器，默认使用字节序比较
  const Comparator* comparator;
  
  // MemTable刷盘阈值（字节）
  size_t memtable_flush_threshold;
  
  // 数据库目录路径
  std::string db_path;
  
  // 构造函数设置默认值
  Options();
};

// 主数据库类，提供LSM-Tree存储引擎的完整接口
// 线程安全：支持多线程并发读写
class DB {
 public:
  // 打开或创建数据库
  // options: 数据库配置选项
  // db: 输出参数，成功时包含创建的DB实例
  static Status Open(const Options& options, std::unique_ptr<DB>* db);

  // 析构函数，自动关闭数据库
  ~DB();

  // 禁止拷贝和赋值
  DB(const DB&) = delete;
  DB& operator=(const DB&) = delete;

  // 写入key-value对
  // key: 键
  // value: 值
  // 返回：成功返回OK，失败返回相应错误状态
  Status Put(const Slice& key, const Slice& value);

  // 读取指定key的value
  // key: 要查找的键
  // value: 输出参数，找到时存储对应的值
  // 返回：成功找到返回OK，未找到返回NotFound，其他错误返回相应状态
  Status Get(const Slice& key, std::string* value);

  // 删除指定key
  // key: 要删除的键
  // 返回：成功返回OK（无论key是否存在），失败返回相应错误状态
  Status Delete(const Slice& key);

  // 手动触发MemTable刷盘
  // 通常不需要手动调用，Put操作会自动触发
  Status FlushMemTable();

  // 获取数据库统计信息
  struct Stats {
    size_t memtable_size;        // 当前MemTable大小（字节）
    size_t num_sstables;         // SSTable文件数量
    size_t total_sstable_size;   // 所有SSTable文件总大小（字节）
    uint64_t num_entries;        // 估算的总条目数量
  };
  
  void GetStats(Stats* stats);

  // 关闭数据库，刷新所有未持久化的数据
  Status Close();

 private:
  // 私有构造函数，只能通过Open()创建
  explicit DB(const Options& options);

  // 初始化数据库：创建目录、加载现有SSTable等
  Status Initialize();

  // 检查是否需要刷盘，如果需要则执行刷盘
  Status MaybeScheduleFlush();

  // 加载目录中现有的SSTable文件
  Status LoadSSTables();

  // 生成下一个SSTable文件的序列号
  uint64_t GetNextFileNumber();

  // 内部实现结构体，隐藏实现细节
  struct Rep;
  Rep* rep_;
};

}  // namespace kvstore

#endif  // KVSTORE_DB_H_
