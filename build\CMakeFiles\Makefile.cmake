# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/mnt/hgfs/my_share/kvstore/CMakeLists.txt"
  "CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestTargets-none.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestTargets.cmake"
  "/usr/share/cmake-3.28/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in"
  "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake"
  "/usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.28/Modules/FindGTest.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.28/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.28/Modules/GNUInstallDirs.cmake"
  "/usr/share/cmake-3.28/Modules/GoogleTest.cmake"
  "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  "/usr/share/cmake-3.28/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "MyKVStoreConfigVersion.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/kvstore.dir/DependInfo.cmake"
  "CMakeFiles/slice_test.dir/DependInfo.cmake"
  "CMakeFiles/skiplist_test.dir/DependInfo.cmake"
  "CMakeFiles/memtable_test.dir/DependInfo.cmake"
  "CMakeFiles/sstable_test.dir/DependInfo.cmake"
  "CMakeFiles/run_tests.dir/DependInfo.cmake"
  )
