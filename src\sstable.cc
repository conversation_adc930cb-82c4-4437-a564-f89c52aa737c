#include "kvstore/sstable.h"
#include "kvstore/comparator.h"
#include <fstream>
#include <cassert>
#include <cstdint>
#include <vector>

namespace kvstore {

// 魔数和版本号定义（与Builder保持一致）
static const uint32_t kSSTableMagicNumber = 0x12345678;
static const uint32_t kSSTableVersion = 1;

// Varint解码辅助函数 - 复用MemTable的实现
static const char* GetVarint32Ptr(const char* p, const char* limit, uint32_t* value) {
  if (p < limit) {
    uint32_t result = *(reinterpret_cast<const uint8_t*>(p));
    if ((result & 128) == 0) {
      *value = result;
      return p + 1;
    }
  }
  
  // 多字节varint的完整解码
  uint32_t result = 0;
  for (uint32_t shift = 0; shift <= 28 && p < limit; shift += 7) {
    uint32_t byte = *(reinterpret_cast<const uint8_t*>(p));
    p++;
    if (byte & 128) {
      result |= ((byte & 127) << shift);
    } else {
      result |= (byte << shift);
      *value = result;
      return p;
    }
  }
  return nullptr;  // 解码失败
}

static const char* GetVarint64Ptr(const char* p, const char* limit, uint64_t* value) {
  uint64_t result = 0;
  for (uint32_t shift = 0; shift <= 63 && p < limit; shift += 7) {
    uint64_t byte = *(reinterpret_cast<const uint8_t*>(p));
    p++;
    if (byte & 128) {
      result |= ((byte & 127) << shift);
    } else {
      result |= (byte << shift);
      *value = result;
      return p;
    }
  }
  return nullptr;  // 解码失败
}

static uint32_t DecodeFixed32(const char* ptr) {
  return ((static_cast<uint32_t>(static_cast<uint8_t>(ptr[0]))) |
          (static_cast<uint32_t>(static_cast<uint8_t>(ptr[1])) << 8) |
          (static_cast<uint32_t>(static_cast<uint8_t>(ptr[2])) << 16) |
          (static_cast<uint32_t>(static_cast<uint8_t>(ptr[3])) << 24));
}

static Slice GetLengthPrefixedSlice(const char* data) {
  uint32_t len;
  const char* p = GetVarint32Ptr(data, data + 5, &len);  // varint最多5字节
  return Slice(p, len);
}

// SSTable内部实现结构
struct SSTable::Rep {
  std::string filename;           // 文件名
  const Comparator* comparator;   // 比较器
  std::string file_content;       // 文件内容缓存
  uint64_t num_entries;          // 条目数量
  uint64_t file_size;            // 文件大小
  std::string first_key;         // 第一个key
  std::string last_key;          // 最后一个key
  const char* data_start;        // 数据区开始位置
  const char* data_end;          // 数据区结束位置
  
  Rep(const std::string& fname, const Comparator* comp)
      : filename(fname), 
        comparator(comp),
        num_entries(0),
        file_size(0),
        data_start(nullptr),
        data_end(nullptr) {}
};

SSTable::SSTable(const std::string& filename, const Comparator* comparator)
    : rep_(new Rep(filename, comparator)) {
}

SSTable::~SSTable() {
  delete rep_;
}

Status SSTable::Open(const std::string& filename,
                     const Comparator* comparator,
                     std::unique_ptr<SSTable>* sstable) {
  
  auto table = std::unique_ptr<SSTable>(new SSTable(filename, comparator));
  
  // 加载文件内容
  Status s = table->LoadMetadata();
  if (!s.ok()) {
    return s;
  }
  
  *sstable = std::move(table);
  return Status::OK();
}

Status SSTable::LoadMetadata() {
  // 读取整个文件到内存
  std::ifstream file(rep_->filename, std::ios::binary | std::ios::ate);
  if (!file.is_open()) {
    return Status::IOError("无法打开文件");
  }
  
  rep_->file_size = file.tellg();
  if (rep_->file_size < 16) {  // 至少需要header(8) + footer(12)
    return Status::Corruption("文件太小，不是有效的SSTable");
  }
  
  file.seekg(0, std::ios::beg);
  rep_->file_content.resize(rep_->file_size);
  file.read(&rep_->file_content[0], rep_->file_size);
  
  if (file.fail()) {
    return Status::IOError("读取文件失败");
  }
  
  const char* data = rep_->file_content.data();
  
  // 验证文件头：魔数 + 版本号
  if (rep_->file_size < 8) {
    return Status::Corruption("文件头不完整");
  }
  
  uint32_t magic = DecodeFixed32(data);
  uint32_t version = DecodeFixed32(data + 4);
  
  if (magic != kSSTableMagicNumber) {
    return Status::Corruption("魔数不匹配");
  }
  
  if (version != kSSTableVersion) {
    return Status::Corruption("版本号不支持");
  }
  
  // 解析文件尾：从末尾向前解析
  // 文件尾格式：[varint64:num_entries][fixed32:checksum]
  // 先从末尾读取4字节校验和，然后向前解析varint64
  
  if (rep_->file_size < 12) {  // 至少需要header(8) + 最小footer(5字节varint + 4字节checksum)
    return Status::Corruption("文件太小，无法包含完整的footer");
  }
  
  // 从末尾读取校验和（固定4字节）
  const char* checksum_pos = data + rep_->file_size - 4;
  uint32_t stored_checksum = DecodeFixed32(checksum_pos);
  (void)stored_checksum;  // 避免未使用变量警告
  
  // 向前扫描找到varint64的起始位置
  // varint64最长10字节，从校验和前10字节开始尝试解析
  const char* varint_search_start = checksum_pos - 10;
  if (varint_search_start < data + 8) {
    varint_search_start = data + 8;  // 不能超过header边界
  }
  
  const char* varint_start = nullptr;
  for (const char* try_pos = varint_search_start; try_pos < checksum_pos; ++try_pos) {
    const char* end_pos = GetVarint64Ptr(try_pos, checksum_pos, &rep_->num_entries);
    if (end_pos == checksum_pos) {
      // 找到了正确的varint起始位置
      varint_start = try_pos;
      break;
    }
  }
  
  if (varint_start == nullptr) {
    return Status::Corruption("无法解析文件尾的条目数量");
  }
  
  // 设置数据区范围
  rep_->data_start = data + 8;  // 跳过header
  rep_->data_end = varint_start;  // 数据区到varint开始位置
  
  // 如果有数据，提取第一个和最后一个key
  if (rep_->num_entries > 0) {
    // 解析第一个key
    const char* record_start = rep_->data_start;
    if (record_start < rep_->data_end) {
      Slice first_key_slice = GetLengthPrefixedSlice(record_start);
      rep_->first_key.assign(first_key_slice.data(), first_key_slice.size());
    }
    
    // 解析最后一个key需要遍历所有记录（简化版，效率较低）
    const char* current = rep_->data_start;
    std::string last_key_found;
    
    while (current < rep_->data_end) {
      // 解析key
      uint32_t key_len;
      const char* key_start = GetVarint32Ptr(current, rep_->data_end, &key_len);
      if (key_start == nullptr || key_start + key_len > rep_->data_end) {
        break;
      }
      
      last_key_found.assign(key_start, key_len);
      
      // 跳过value
      uint32_t value_len;
      const char* value_start = GetVarint32Ptr(key_start + key_len, rep_->data_end, &value_len);
      if (value_start == nullptr || value_start + value_len > rep_->data_end) {
        break;
      }
      
      current = value_start + value_len;
    }
    
    rep_->last_key = last_key_found;
  }
  
  return Status::OK();
}

Status SSTable::Get(const Slice& key, std::string* value) {
  // 使用Iterator进行查找（简化版实现）
  auto iter = NewIterator();
  iter->Seek(key);
  
  if (iter->Valid() && rep_->comparator->Compare(iter->key(), key) == 0) {
    *value = iter->value().ToString();
    return Status::OK();
  }
  
  return Status::NotFound("键未找到");
}

std::unique_ptr<Iterator> SSTable::NewIterator() {
  return std::make_unique<SSTableIterator>(this);
}

uint64_t SSTable::NumEntries() const {
  return rep_->num_entries;
}

uint64_t SSTable::FileSize() const {
  return rep_->file_size;
}

Slice SSTable::FirstKey() const {
  return Slice(rep_->first_key);
}

Slice SSTable::LastKey() const {
  return Slice(rep_->last_key);
}

// SSTableIterator内部实现结构
struct SSTableIterator::Rep {
  SSTable* sstable;               // 关联的SSTable
  const char* current;            // 当前位置指针
  const char* data_start;         // 数据区开始
  const char* data_end;           // 数据区结束
  bool valid;                     // 当前位置是否有效
  std::string current_key;        // 当前key的缓存
  std::string current_value;      // 当前value的缓存
  
  Rep(SSTable* table) 
      : sstable(table),
        current(nullptr),
        data_start(table->rep_->data_start),
        data_end(table->rep_->data_end),
        valid(false) {}
        
  // 解析当前位置的key-value
  void ParseCurrent() {
    if (current >= data_end) {
      valid = false;
      return;
    }
    
    // 解析key
    uint32_t key_len;
    const char* key_start = GetVarint32Ptr(current, data_end, &key_len);
    if (key_start == nullptr || key_start + key_len > data_end) {
      valid = false;
      return;
    }
    
    current_key.assign(key_start, key_len);
    
    // 解析value
    uint32_t value_len;
    const char* value_start = GetVarint32Ptr(key_start + key_len, data_end, &value_len);
    if (value_start == nullptr || value_start + value_len > data_end) {
      valid = false;
      return;
    }
    
    current_value.assign(value_start, value_len);
    valid = true;
  }
};

SSTableIterator::SSTableIterator(SSTable* sstable)
    : rep_(new Rep(sstable)) {
}

SSTableIterator::~SSTableIterator() {
  delete rep_;
}

bool SSTableIterator::Valid() const {
  return rep_->valid;
}

void SSTableIterator::SeekToFirst() {
  rep_->current = rep_->data_start;
  rep_->ParseCurrent();
}

void SSTableIterator::SeekToLast() {
  // 从数据区末尾向前查找最后一条记录（简化实现，效率较低）
  const char* pos = rep_->data_start;
  const char* last_valid_pos = nullptr;
  
  while (pos < rep_->data_end) {
    last_valid_pos = pos;
    
    // 跳过当前记录
    uint32_t key_len;
    const char* key_start = GetVarint32Ptr(pos, rep_->data_end, &key_len);
    if (key_start == nullptr || key_start + key_len > rep_->data_end) {
      break;
    }
    
    uint32_t value_len;
    const char* value_start = GetVarint32Ptr(key_start + key_len, rep_->data_end, &value_len);
    if (value_start == nullptr || value_start + value_len > rep_->data_end) {
      break;
    }
    
    pos = value_start + value_len;
  }
  
  rep_->current = last_valid_pos;
  rep_->ParseCurrent();
}

void SSTableIterator::Seek(const Slice& target) {
  // 简单的线性查找（MVP版本）
  rep_->current = rep_->data_start;
  
  while (rep_->current < rep_->data_end) {
    rep_->ParseCurrent();
    if (!rep_->valid) {
      break;
    }
    
    int cmp = rep_->sstable->rep_->comparator->Compare(Slice(rep_->current_key), target);
    if (cmp >= 0) {
      return;  // 找到第一个>=target的key
    }
    
    Next();
  }
  
  rep_->valid = false;  // 未找到
}

void SSTableIterator::Next() {
  if (!rep_->valid) {
    return;
  }
  
  // 跳过当前记录
  uint32_t key_len;
  const char* key_start = GetVarint32Ptr(rep_->current, rep_->data_end, &key_len);
  if (key_start == nullptr || key_start + key_len > rep_->data_end) {
    rep_->valid = false;
    return;
  }
  
  uint32_t value_len;
  const char* value_start = GetVarint32Ptr(key_start + key_len, rep_->data_end, &value_len);
  if (value_start == nullptr || value_start + value_len > rep_->data_end) {
    rep_->valid = false;
    return;
  }
  
  rep_->current = value_start + value_len;
  rep_->ParseCurrent();
}

void SSTableIterator::Prev() {
  // 简化实现：不支持向前遍历（MVP版本）
  // 可以通过缓存所有记录位置来实现，但会增加复杂度
  rep_->valid = false;
}

Slice SSTableIterator::key() const {
  assert(rep_->valid);
  return Slice(rep_->current_key);
}

Slice SSTableIterator::value() const {
  assert(rep_->valid);
  return Slice(rep_->current_value);
}

}  // namespace kvstore
