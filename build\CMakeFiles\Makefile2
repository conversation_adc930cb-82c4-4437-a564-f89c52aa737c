# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/hgfs/my_share/kvstore

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/hgfs/my_share/kvstore/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/kvstore.dir/all
all: CMakeFiles/slice_test.dir/all
all: CMakeFiles/skiplist_test.dir/all
all: CMakeFiles/memtable_test.dir/all
all: CMakeFiles/sstable_test.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/kvstore.dir/clean
clean: CMakeFiles/slice_test.dir/clean
clean: CMakeFiles/skiplist_test.dir/clean
clean: CMakeFiles/memtable_test.dir/clean
clean: CMakeFiles/sstable_test.dir/clean
clean: CMakeFiles/run_tests.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/kvstore.dir

# All Build rule for target.
CMakeFiles/kvstore.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8 "Built target kvstore"
.PHONY : CMakeFiles/kvstore.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/kvstore.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/kvstore.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : CMakeFiles/kvstore.dir/rule

# Convenience name for target.
kvstore: CMakeFiles/kvstore.dir/rule
.PHONY : kvstore

# clean rule for target.
CMakeFiles/kvstore.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kvstore.dir/build.make CMakeFiles/kvstore.dir/clean
.PHONY : CMakeFiles/kvstore.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/slice_test.dir

# All Build rule for target.
CMakeFiles/slice_test.dir/all: CMakeFiles/kvstore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=14,15 "Built target slice_test"
.PHONY : CMakeFiles/slice_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/slice_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/slice_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : CMakeFiles/slice_test.dir/rule

# Convenience name for target.
slice_test: CMakeFiles/slice_test.dir/rule
.PHONY : slice_test

# clean rule for target.
CMakeFiles/slice_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/slice_test.dir/build.make CMakeFiles/slice_test.dir/clean
.PHONY : CMakeFiles/slice_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/skiplist_test.dir

# All Build rule for target.
CMakeFiles/skiplist_test.dir/all: CMakeFiles/kvstore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=12,13 "Built target skiplist_test"
.PHONY : CMakeFiles/skiplist_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/skiplist_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/skiplist_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : CMakeFiles/skiplist_test.dir/rule

# Convenience name for target.
skiplist_test: CMakeFiles/skiplist_test.dir/rule
.PHONY : skiplist_test

# clean rule for target.
CMakeFiles/skiplist_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/skiplist_test.dir/build.make CMakeFiles/skiplist_test.dir/clean
.PHONY : CMakeFiles/skiplist_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/memtable_test.dir

# All Build rule for target.
CMakeFiles/memtable_test.dir/all: CMakeFiles/kvstore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=9,10 "Built target memtable_test"
.PHONY : CMakeFiles/memtable_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/memtable_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/memtable_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : CMakeFiles/memtable_test.dir/rule

# Convenience name for target.
memtable_test: CMakeFiles/memtable_test.dir/rule
.PHONY : memtable_test

# clean rule for target.
CMakeFiles/memtable_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/memtable_test.dir/build.make CMakeFiles/memtable_test.dir/clean
.PHONY : CMakeFiles/memtable_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/sstable_test.dir

# All Build rule for target.
CMakeFiles/sstable_test.dir/all: CMakeFiles/kvstore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=16,17 "Built target sstable_test"
.PHONY : CMakeFiles/sstable_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/sstable_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/sstable_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : CMakeFiles/sstable_test.dir/rule

# Convenience name for target.
sstable_test: CMakeFiles/sstable_test.dir/rule
.PHONY : sstable_test

# clean rule for target.
CMakeFiles/sstable_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sstable_test.dir/build.make CMakeFiles/sstable_test.dir/clean
.PHONY : CMakeFiles/sstable_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run_tests.dir

# All Build rule for target.
CMakeFiles/run_tests.dir/all: CMakeFiles/memtable_test.dir/all
CMakeFiles/run_tests.dir/all: CMakeFiles/sstable_test.dir/all
CMakeFiles/run_tests.dir/all: CMakeFiles/slice_test.dir/all
CMakeFiles/run_tests.dir/all: CMakeFiles/skiplist_test.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=11 "Built target run_tests"
.PHONY : CMakeFiles/run_tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/run_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/hgfs/my_share/kvstore/build/CMakeFiles 0
.PHONY : CMakeFiles/run_tests.dir/rule

# Convenience name for target.
run_tests: CMakeFiles/run_tests.dir/rule
.PHONY : run_tests

# clean rule for target.
CMakeFiles/run_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/clean
.PHONY : CMakeFiles/run_tests.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

