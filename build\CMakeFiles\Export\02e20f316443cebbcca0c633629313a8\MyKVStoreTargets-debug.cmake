#----------------------------------------------------------------
# Generated CMake target import file for configuration "Debug".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "MyKVStore::kvstore" for configuration "Debug"
set_property(TARGET MyKVStore::kvstore APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
set_target_properties(MyKVStore::kvstore PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_DEBUG "CXX"
  IMPORTED_LOCATION_DEBUG "${_IMPORT_PREFIX}/lib/libkvstore.a"
  )

list(APPEND _cmake_import_check_targets MyKVStore::kvstore )
list(APPEND _cmake_import_check_files_for_MyKVStore::kvstore "${_IMPORT_PREFIX}/lib/libkvstore.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
