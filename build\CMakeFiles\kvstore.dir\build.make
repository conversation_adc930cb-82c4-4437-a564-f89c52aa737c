# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/hgfs/my_share/kvstore

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/hgfs/my_share/kvstore/build

# Include any dependencies generated for this target.
include CMakeFiles/kvstore.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/kvstore.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/kvstore.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/kvstore.dir/flags.make

CMakeFiles/kvstore.dir/src/slice.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/slice.cc.o: /mnt/hgfs/my_share/kvstore/src/slice.cc
CMakeFiles/kvstore.dir/src/slice.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/kvstore.dir/src/slice.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/slice.cc.o -MF CMakeFiles/kvstore.dir/src/slice.cc.o.d -o CMakeFiles/kvstore.dir/src/slice.cc.o -c /mnt/hgfs/my_share/kvstore/src/slice.cc

CMakeFiles/kvstore.dir/src/slice.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/slice.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/slice.cc > CMakeFiles/kvstore.dir/src/slice.cc.i

CMakeFiles/kvstore.dir/src/slice.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/slice.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/slice.cc -o CMakeFiles/kvstore.dir/src/slice.cc.s

CMakeFiles/kvstore.dir/src/status.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/status.cc.o: /mnt/hgfs/my_share/kvstore/src/status.cc
CMakeFiles/kvstore.dir/src/status.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/kvstore.dir/src/status.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/status.cc.o -MF CMakeFiles/kvstore.dir/src/status.cc.o.d -o CMakeFiles/kvstore.dir/src/status.cc.o -c /mnt/hgfs/my_share/kvstore/src/status.cc

CMakeFiles/kvstore.dir/src/status.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/status.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/status.cc > CMakeFiles/kvstore.dir/src/status.cc.i

CMakeFiles/kvstore.dir/src/status.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/status.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/status.cc -o CMakeFiles/kvstore.dir/src/status.cc.s

CMakeFiles/kvstore.dir/src/comparator.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/comparator.cc.o: /mnt/hgfs/my_share/kvstore/src/comparator.cc
CMakeFiles/kvstore.dir/src/comparator.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/kvstore.dir/src/comparator.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/comparator.cc.o -MF CMakeFiles/kvstore.dir/src/comparator.cc.o.d -o CMakeFiles/kvstore.dir/src/comparator.cc.o -c /mnt/hgfs/my_share/kvstore/src/comparator.cc

CMakeFiles/kvstore.dir/src/comparator.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/comparator.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/comparator.cc > CMakeFiles/kvstore.dir/src/comparator.cc.i

CMakeFiles/kvstore.dir/src/comparator.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/comparator.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/comparator.cc -o CMakeFiles/kvstore.dir/src/comparator.cc.s

CMakeFiles/kvstore.dir/src/memtable.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/memtable.cc.o: /mnt/hgfs/my_share/kvstore/src/memtable.cc
CMakeFiles/kvstore.dir/src/memtable.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/kvstore.dir/src/memtable.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/memtable.cc.o -MF CMakeFiles/kvstore.dir/src/memtable.cc.o.d -o CMakeFiles/kvstore.dir/src/memtable.cc.o -c /mnt/hgfs/my_share/kvstore/src/memtable.cc

CMakeFiles/kvstore.dir/src/memtable.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/memtable.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/memtable.cc > CMakeFiles/kvstore.dir/src/memtable.cc.i

CMakeFiles/kvstore.dir/src/memtable.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/memtable.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/memtable.cc -o CMakeFiles/kvstore.dir/src/memtable.cc.s

CMakeFiles/kvstore.dir/src/sstable_builder.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/sstable_builder.cc.o: /mnt/hgfs/my_share/kvstore/src/sstable_builder.cc
CMakeFiles/kvstore.dir/src/sstable_builder.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/kvstore.dir/src/sstable_builder.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/sstable_builder.cc.o -MF CMakeFiles/kvstore.dir/src/sstable_builder.cc.o.d -o CMakeFiles/kvstore.dir/src/sstable_builder.cc.o -c /mnt/hgfs/my_share/kvstore/src/sstable_builder.cc

CMakeFiles/kvstore.dir/src/sstable_builder.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/sstable_builder.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/sstable_builder.cc > CMakeFiles/kvstore.dir/src/sstable_builder.cc.i

CMakeFiles/kvstore.dir/src/sstable_builder.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/sstable_builder.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/sstable_builder.cc -o CMakeFiles/kvstore.dir/src/sstable_builder.cc.s

CMakeFiles/kvstore.dir/src/sstable.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/sstable.cc.o: /mnt/hgfs/my_share/kvstore/src/sstable.cc
CMakeFiles/kvstore.dir/src/sstable.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/kvstore.dir/src/sstable.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/sstable.cc.o -MF CMakeFiles/kvstore.dir/src/sstable.cc.o.d -o CMakeFiles/kvstore.dir/src/sstable.cc.o -c /mnt/hgfs/my_share/kvstore/src/sstable.cc

CMakeFiles/kvstore.dir/src/sstable.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/sstable.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/sstable.cc > CMakeFiles/kvstore.dir/src/sstable.cc.i

CMakeFiles/kvstore.dir/src/sstable.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/sstable.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/sstable.cc -o CMakeFiles/kvstore.dir/src/sstable.cc.s

CMakeFiles/kvstore.dir/src/db.cc.o: CMakeFiles/kvstore.dir/flags.make
CMakeFiles/kvstore.dir/src/db.cc.o: /mnt/hgfs/my_share/kvstore/src/db.cc
CMakeFiles/kvstore.dir/src/db.cc.o: CMakeFiles/kvstore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/kvstore.dir/src/db.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/kvstore.dir/src/db.cc.o -MF CMakeFiles/kvstore.dir/src/db.cc.o.d -o CMakeFiles/kvstore.dir/src/db.cc.o -c /mnt/hgfs/my_share/kvstore/src/db.cc

CMakeFiles/kvstore.dir/src/db.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/kvstore.dir/src/db.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/hgfs/my_share/kvstore/src/db.cc > CMakeFiles/kvstore.dir/src/db.cc.i

CMakeFiles/kvstore.dir/src/db.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/kvstore.dir/src/db.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/hgfs/my_share/kvstore/src/db.cc -o CMakeFiles/kvstore.dir/src/db.cc.s

# Object files for target kvstore
kvstore_OBJECTS = \
"CMakeFiles/kvstore.dir/src/slice.cc.o" \
"CMakeFiles/kvstore.dir/src/status.cc.o" \
"CMakeFiles/kvstore.dir/src/comparator.cc.o" \
"CMakeFiles/kvstore.dir/src/memtable.cc.o" \
"CMakeFiles/kvstore.dir/src/sstable_builder.cc.o" \
"CMakeFiles/kvstore.dir/src/sstable.cc.o" \
"CMakeFiles/kvstore.dir/src/db.cc.o"

# External object files for target kvstore
kvstore_EXTERNAL_OBJECTS =

libkvstore.a: CMakeFiles/kvstore.dir/src/slice.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/src/status.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/src/comparator.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/src/memtable.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/src/sstable_builder.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/src/sstable.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/src/db.cc.o
libkvstore.a: CMakeFiles/kvstore.dir/build.make
libkvstore.a: CMakeFiles/kvstore.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/mnt/hgfs/my_share/kvstore/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX static library libkvstore.a"
	$(CMAKE_COMMAND) -P CMakeFiles/kvstore.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/kvstore.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/kvstore.dir/build: libkvstore.a
.PHONY : CMakeFiles/kvstore.dir/build

CMakeFiles/kvstore.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/kvstore.dir/cmake_clean.cmake
.PHONY : CMakeFiles/kvstore.dir/clean

CMakeFiles/kvstore.dir/depend:
	cd /mnt/hgfs/my_share/kvstore/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/hgfs/my_share/kvstore /mnt/hgfs/my_share/kvstore /mnt/hgfs/my_share/kvstore/build /mnt/hgfs/my_share/kvstore/build /mnt/hgfs/my_share/kvstore/build/CMakeFiles/kvstore.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/kvstore.dir/depend

